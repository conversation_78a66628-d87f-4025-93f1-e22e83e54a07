#%%
import os
import time
import six
from functools import reduce
import struct
import pandas as pd

import pickle
import traceback
#%%
fn  = open(r'D:\BaiduNetdiskDownload\EURUSD60_0\EURUSD60_0.fxt', 'rb')
fn.seek(728)
out_f = open(r'D:\BaiduNetdiskDownload\EURUSD60_0\EURUSD60_0.csv','w')
out_f.write('CurrentBarTime,Open,Low,High,Close,Volume,IncomingTickTime,Flag\r')
#%%
%%time
struct_list = []
for iii in range(100*10000):
    try:
        chunk_data = fn.read(56)# .hex()
    except:
        break
    #print(chunk_data)
    fmt = 'idddddii'
    unpacked_data = struct.unpack(fmt, chunk_data)
    struct_list.append(unpacked_data)
#%%
%%time
test_df = pd.DataFrame(struct_list)
test_df.columns = ['CurrentBarTime', 'Open', 'Low', 'High', 'Close', 'Volume', 'IncomingTickTime', 'Flag']
test_df['CurrentBarTime'] = test_df['CurrentBarTime'].apply(lambda x: time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(x)))
test_df['IncomingTickTime'] = test_df['IncomingTickTime'].apply(lambda x: time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(x)))
#%%
test_df
#%%
test_df.to_csv('demo.csv')
#%%
out_f.close() 
#%%
%%time
# 写出数据
struct_list = []
#for iii in range(10*10000):
while True:
    try:
        chunk_data = fn.read(56)# .hex()
    except:
        break
    if len(chunk_data) == 0:
        break
    #print(chunk_data)
    fmt = 'idddddii'
    unpacked_data = struct.unpack(fmt, chunk_data)
    out_f.write(','.join(map(str, unpacked_data))+ '\r')
#%%
out_f.close() 
#%%

#%%

#%%

#%%

#%%

#%%
byte_num = 0
for line in fn.readline():
    break
#%%
line
#%%

#%%
