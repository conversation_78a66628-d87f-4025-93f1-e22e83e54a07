"""
订单数据访问对象

提供订单相关的数据访问操作
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func
from sqlalchemy.exc import SQLAlchemyError

from .base_dao import BaseDAO
from custom_api.database.connection import get_db_session
from custom_api.models.order import (
    Order, OrderStatus, OrderDirection, OrderType,
    OpenCloseType
)
from custom_api import qlog


class OrderDAO(BaseDAO[Order, Dict[str, Any], Dict[str, Any]]):
    """订单数据访问对象"""
    
    def __init__(self):
        super().__init__(Order)
    
    def get_by_order_no(self, order_no: str, session: Optional[Session] = None) -> Optional[Order]:
        """
        根据订单号获取订单
        
        Args:
            order_no: 订单号
            session: 数据库会话
            
        Returns:
            订单实例或None
        """
        return self.get_by_field('order_no', order_no, session)
    
    def get_by_symbol(self, symbol: str, status: Optional[OrderStatus] = None,
                      skip: int = 0, limit: int = 100,
                      session: Optional[Session] = None) -> List[Order]:
        """
        根据合约获取订单列表
        
        Args:
            symbol: 合约代码
            status: 订单状态（可选）
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            订单列表
        """
        def _get_by_symbol(db_session: Session) -> List[Order]:
            try:
                query = db_session.query(Order).filter(Order.symbol == symbol)
                if status:
                    query = query.filter(Order.status == status)
                
                result = (query.order_by(desc(Order.order_time))
                         .offset(skip)
                         .limit(limit)
                         .all())
                
                qlog.info(f"根据合约查询订单: {symbol}, 状态={status}, 结果数量={len(result)}")
                return result
            except Exception as e:
                qlog.error(f"根据合约查询订单失败: {e}")
                raise
        
        if session:
            return _get_by_symbol(session)
        else:
            with get_db_session() as db_session:
                return _get_by_symbol(db_session)
    
    def get_by_status(self, status: OrderStatus, skip: int = 0, limit: int = 100,
                      session: Optional[Session] = None) -> List[Order]:
        """
        根据状态获取订单列表
        
        Args:
            status: 订单状态
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            订单列表
        """
        return self.filter_by({'status': status}, skip, limit, 'order_time', True, session)
    
    def get_pending_orders(self, skip: int = 0, limit: int = 100,
                          session: Optional[Session] = None) -> List[Order]:
        """
        获取挂单中的订单列表
        
        Args:
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            挂单订单列表
        """
        return self.get_by_status(OrderStatus.PENDING, skip, limit, session)
    
    def get_filled_orders(self, skip: int = 0, limit: int = 100,
                         session: Optional[Session] = None) -> List[Order]:
        """
        获取已成交的订单列表
        
        Args:
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            已成交订单列表
        """
        return self.get_by_status(OrderStatus.FILLED, skip, limit, session)
    
    def get_by_channel(self, channel_code: str, skip: int = 0, limit: int = 100,
                       session: Optional[Session] = None) -> List[Order]:
        """
        根据渠道获取订单列表
        
        Args:
            channel_code: 渠道编号
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            订单列表
        """
        return self.filter_by({'channel_code': channel_code}, skip, limit, 'order_time', True, session)
    
    def get_by_date_range(self, start_date: datetime, end_date: datetime,
                          skip: int = 0, limit: int = 100,
                          session: Optional[Session] = None) -> List[Order]:
        """
        根据日期范围获取订单列表
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            订单列表
        """
        def _get_by_date_range(db_session: Session) -> List[Order]:
            try:
                result = (db_session.query(Order)
                         .filter(Order.order_time.between(start_date, end_date))
                         .order_by(desc(Order.order_time))
                         .offset(skip)
                         .limit(limit)
                         .all())
                qlog.info(f"根据日期范围查询订单: {start_date} - {end_date}, 结果数量={len(result)}")
                return result
            except Exception as e:
                qlog.error(f"根据日期范围查询订单失败: {e}")
                raise
        
        if session:
            return _get_by_date_range(session)
        else:
            with get_db_session() as db_session:
                return _get_by_date_range(db_session)
    
    def update_order_status(self, order_no: str, status: OrderStatus, 
                           filled_quantity: Optional[float] = None,
                           session: Optional[Session] = None) -> bool:
        """
        更新订单状态
        
        Args:
            order_no: 订单号
            status: 新状态
            filled_quantity: 成交数量（可选）
            session: 数据库会话
            
        Returns:
            是否更新成功
        """
        def _update_status(db_session: Session) -> bool:
            try:
                order = db_session.query(Order).filter(Order.order_no == order_no).first()
                if not order:
                    qlog.info(f"订单不存在: {order_no}")
                    return False

                order.status = status
                if filled_quantity is not None:
                    order.filled_quantity = filled_quantity

                db_session.commit()
                qlog.info(f"订单状态更新成功: {order_no} -> {status}")
                return True

            except Exception as e:
                qlog.error(f"更新订单状态失败: {e}")
                db_session.rollback()
                raise
        
        if session:
            return _update_status(session)
        else:
            with get_db_session() as db_session:
                return _update_status(db_session)
    
    def get_statistics_by_symbol(self, symbol: str, session: Optional[Session] = None) -> Dict[str, Any]:
        """
        获取合约的订单统计信息
        
        Args:
            symbol: 合约代码
            session: 数据库会话
            
        Returns:
            统计信息字典
        """
        def _get_statistics(db_session: Session) -> Dict[str, Any]:
            try:
                # 基础统计
                total_count = db_session.query(Order).filter(Order.symbol == symbol).count()
                
                if total_count == 0:
                    return {
                        'symbol': symbol,
                        'total_count': 0,
                        'pending_count': 0,
                        'filled_count': 0,
                        'cancelled_count': 0,
                        'total_quantity': 0,
                        'filled_quantity': 0
                    }
                
                # 按状态统计
                status_stats = (db_session.query(
                    Order.status,
                    func.count(Order.id).label('count'),
                    func.sum(Order.quantity).label('quantity'),
                    func.sum(Order.filled_quantity).label('filled_quantity')
                ).filter(Order.symbol == symbol)
                .group_by(Order.status)
                .all())
                
                stats = {
                    'symbol': symbol,
                    'total_count': total_count,
                    'pending_count': 0,
                    'filled_count': 0,
                    'cancelled_count': 0,
                    'total_quantity': 0,
                    'filled_quantity': 0
                }
                
                for stat in status_stats:
                    if stat.status == OrderStatus.PENDING:
                        stats['pending_count'] = stat.count
                    elif stat.status == OrderStatus.FILLED:
                        stats['filled_count'] = stat.count
                    elif stat.status == OrderStatus.CANCELLED:
                        stats['cancelled_count'] = stat.count
                    
                    stats['total_quantity'] += float(stat.quantity or 0)
                    stats['filled_quantity'] += float(stat.filled_quantity or 0)
                
                return stats
                
            except Exception as e:
                qlog.error(f"获取合约统计信息失败: {e}")
                raise
        
        if session:
            return _get_statistics(session)
        else:
            with get_db_session() as db_session:
                return _get_statistics(db_session)


# 全局订单DAO实例
order_dao = OrderDAO()


def get_order_dao() -> OrderDAO:
    """获取订单DAO实例"""
    return order_dao
