"""
交易数据访问对象

提供交易表的数据访问操作
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, date
from decimal import Decimal
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func, between

from .base_dao import BaseDAO
from custom_api.models.trade import Trade, SideType, EffectType, TradeType
from custom_api.database.connection import get_db_session
from custom_api import qlog


class TradeDAO(BaseDAO[Trade, Dict[str, Any], Dict[str, Any]]):
    """交易数据访问对象"""
    
    def __init__(self):
        super().__init__(Trade)
    
    def get_by_order_no(self, order_no: str, session: Optional[Session] = None) -> List[Trade]:
        """
        根据订单号获取交易列表
        
        Args:
            order_no: 订单号
            session: 数据库会话
            
        Returns:
            交易列表
        """
        def _get_by_order_no(db_session: Session) -> List[Trade]:
            try:
                result = db_session.query(Trade).filter(Trade.order_no == order_no).all()
                qlog.info(f"根据订单号查询交易: {order_no}, 结果数量={len(result)}")
                return result
            except Exception as e:
                qlog.error(f"根据订单号查询交易失败: {e}")
                raise
        
        if session:
            return _get_by_order_no(session)
        else:
            with get_db_session() as db_session:
                return _get_by_order_no(db_session)
    
    def get_by_order_id(self, order_id: int, session: Optional[Session] = None) -> List[Trade]:
        """
        根据订单ID获取交易列表
        
        Args:
            order_id: 订单ID
            session: 数据库会话
            
        Returns:
            交易列表
        """
        def _get_by_order_id(db_session: Session) -> List[Trade]:
            try:
                result = db_session.query(Trade).filter(Trade.order_id == order_id).all()
                qlog.info(f"根据订单ID查询交易: {order_id}, 结果数量={len(result)}")
                return result
            except Exception as e:
                qlog.error(f"根据订单ID查询交易失败: {e}")
                raise
        
        if session:
            return _get_by_order_id(session)
        else:
            with get_db_session() as db_session:
                return _get_by_order_id(db_session)
    
    def get_by_execution_id(self, execution_id: str, session: Optional[Session] = None) -> Optional[Trade]:
        """
        根据成交号获取交易
        
        Args:
            execution_id: 成交号
            session: 数据库会话
            
        Returns:
            交易实例或None
        """
        return self.get_by_field('execution_id', execution_id, session)
    
    def get_by_symbol(self, symbol: str, skip: int = 0, limit: int = 100,
                      session: Optional[Session] = None) -> List[Trade]:
        """
        根据合约代码获取交易列表
        
        Args:
            symbol: 合约代码
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            交易列表
        """
        return self.filter_by({'symbol': symbol}, skip, limit, 'execution_time', True, session)
    
    def get_by_channel(self, channel_code: str, skip: int = 0, limit: int = 100,
                       session: Optional[Session] = None) -> List[Trade]:
        """
        根据渠道编号获取交易列表
        
        Args:
            channel_code: 渠道编号
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            交易列表
        """
        return self.filter_by({'channel_code': channel_code}, skip, limit, 'execution_time', True, session)
    
    def get_by_side(self, side: SideType, skip: int = 0, limit: int = 100,
                    session: Optional[Session] = None) -> List[Trade]:
        """
        根据成交方向获取交易列表
        
        Args:
            side: 成交方向
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            交易列表
        """
        return self.filter_by({'side': side}, skip, limit, 'execution_time', True, session)
    
    def get_by_date_range(self, start_date: datetime, end_date: datetime,
                          skip: int = 0, limit: int = 100,
                          session: Optional[Session] = None) -> List[Trade]:
        """
        根据日期范围获取交易列表
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            交易列表
        """
        def _get_by_date_range(db_session: Session) -> List[Trade]:
            try:
                result = (db_session.query(Trade)
                         .filter(between(Trade.execution_time, start_date, end_date))
                         .order_by(desc(Trade.execution_time))
                         .offset(skip)
                         .limit(limit)
                         .all())
                qlog.info(f"根据日期范围查询交易: {start_date} - {end_date}, 结果数量={len(result)}")
                return result
            except Exception as e:
                qlog.error(f"根据日期范围查询交易失败: {e}")
                raise
        
        if session:
            return _get_by_date_range(session)
        else:
            with get_db_session() as db_session:
                return _get_by_date_range(db_session)
    
    def get_statistics_by_symbol(self, symbol: str, session: Optional[Session] = None) -> Dict[str, Any]:
        """
        获取合约的交易统计信息
        
        Args:
            symbol: 合约代码
            session: 数据库会话
            
        Returns:
            统计信息字典
        """
        def _get_statistics(db_session: Session) -> Dict[str, Any]:
            try:
                # 基础统计
                total_count = db_session.query(Trade).filter(Trade.symbol == symbol).count()
                
                if total_count == 0:
                    return {
                        'symbol': symbol,
                        'total_count': 0,
                        'total_volume': 0,
                        'total_amount': 0,
                        'avg_price': 0,
                        'buy_count': 0,
                        'sell_count': 0,
                        'buy_volume': 0,
                        'sell_volume': 0
                    }
                
                # 聚合查询
                stats = (db_session.query(
                    func.count(Trade.id).label('count'),
                    func.sum(Trade.execution_quantity).label('volume'),
                    func.sum(Trade.execution_price * Trade.execution_quantity).label('amount'),
                    func.avg(Trade.execution_price).label('avg_price')
                ).filter(Trade.symbol == symbol).first())
                
                # 按方向统计
                buy_stats = (db_session.query(
                    func.count(Trade.id).label('count'),
                    func.sum(Trade.execution_quantity).label('volume')
                ).filter(and_(Trade.symbol == symbol, Trade.side == SideType.BUY)).first())
                
                sell_stats = (db_session.query(
                    func.count(Trade.id).label('count'),
                    func.sum(Trade.execution_quantity).label('volume')
                ).filter(and_(Trade.symbol == symbol, Trade.side == SideType.SELL)).first())
                
                return {
                    'symbol': symbol,
                    'total_count': stats.count or 0,
                    'total_volume': float(stats.volume or 0),
                    'total_amount': float(stats.amount or 0),
                    'avg_price': float(stats.avg_price or 0),
                    'buy_count': buy_stats.count or 0,
                    'sell_count': sell_stats.count or 0,
                    'buy_volume': float(buy_stats.volume or 0),
                    'sell_volume': float(sell_stats.volume or 0)
                }
                
            except Exception as e:
                qlog.error(f"获取合约统计信息失败: {e}")
                raise
        
        if session:
            return _get_statistics(session)
        else:
            with get_db_session() as db_session:
                return _get_statistics(db_session)


# 全局交易DAO实例
trade_dao = TradeDAO()


def get_trade_dao() -> TradeDAO:
    """获取交易DAO实例"""
    return trade_dao
