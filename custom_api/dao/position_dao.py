"""
持仓数据访问对象

提供持仓表的数据访问操作
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, date
from decimal import Decimal
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func, between

from .base_dao import BaseDAO
from custom_api.models.position import Position, PositionSide, PositionStatus, PositionType
from custom_api.database.connection import get_db_session
from custom_api import qlog


class PositionDAO(BaseDAO[Position, Dict[str, Any], Dict[str, Any]]):
    """持仓数据访问对象"""
    
    def __init__(self):
        super().__init__(Position)
    
    def get_by_position_id(self, position_id: str, session: Optional[Session] = None) -> Optional[Position]:
        """
        根据持仓编号获取持仓
        
        Args:
            position_id: 持仓编号
            session: 数据库会话
            
        Returns:
            持仓实例或None
        """
        return self.get_by_field('position_id', position_id, session)
    
    def get_by_open_order_id(self, open_order_id: str, session: Optional[Session] = None) -> Optional[Position]:
        """
        根据开仓订单ID获取持仓
        
        Args:
            open_order_id: 开仓订单ID
            session: 数据库会话
            
        Returns:
            持仓实例或None
        """
        return self.get_by_field('open_order_id', open_order_id, session)
    
    def get_by_symbol(self, symbol: str, skip: int = 0, limit: int = 100,
                      session: Optional[Session] = None) -> List[Position]:
        """
        根据合约代码获取持仓列表
        
        Args:
            symbol: 合约代码
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            持仓列表
        """
        return self.filter_by({'symbol': symbol}, skip, limit, 'open_time', True, session)
    
    def get_by_channel(self, channel_code: str, skip: int = 0, limit: int = 100,
                       session: Optional[Session] = None) -> List[Position]:
        """
        根据渠道编号获取持仓列表
        
        Args:
            channel_code: 渠道编号
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            持仓列表
        """
        return self.filter_by({'channel_code': channel_code}, skip, limit, 'open_time', True, session)
    
    def get_by_side(self, position_side: PositionSide, skip: int = 0, limit: int = 100,
                    session: Optional[Session] = None) -> List[Position]:
        """
        根据持仓方向获取持仓列表
        
        Args:
            position_side: 持仓方向
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            持仓列表
        """
        return self.filter_by({'position_side': position_side}, skip, limit, 'open_time', True, session)
    
    def get_active_positions(self, skip: int = 0, limit: int = 100,
                            session: Optional[Session] = None) -> List[Position]:
        """
        获取活跃持仓列表
        
        Args:
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            活跃持仓列表
        """
        return self.filter_by({'is_active': True}, skip, limit, 'open_time', True, session)
    
    def get_open_positions(self, skip: int = 0, limit: int = 100,
                          session: Optional[Session] = None) -> List[Position]:
        """
        获取开仓状态的持仓列表
        
        Args:
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            开仓持仓列表
        """
        filters = {'position_status': PositionStatus.OPEN, 'is_active': True}
        return self.filter_by(filters, skip, limit, 'open_time', True, session)
    
    def get_closed_positions(self, skip: int = 0, limit: int = 100,
                            session: Optional[Session] = None) -> List[Position]:
        """
        获取已平仓的持仓列表
        
        Args:
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            已平仓持仓列表
        """
        return self.filter_by({'position_status': PositionStatus.CLOSED}, skip, limit, 'close_time', True, session)
    
    def get_by_date_range(self, start_date: datetime, end_date: datetime,
                          skip: int = 0, limit: int = 100,
                          session: Optional[Session] = None) -> List[Position]:
        """
        根据开仓日期范围获取持仓列表
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            持仓列表
        """
        def _get_by_date_range(db_session: Session) -> List[Position]:
            try:
                result = (db_session.query(Position)
                         .filter(between(Position.open_time, start_date, end_date))
                         .order_by(desc(Position.open_time))
                         .offset(skip)
                         .limit(limit)
                         .all())
                qlog.info(f"根据日期范围查询持仓: {start_date} - {end_date}, 结果数量={len(result)}")
                return result
            except Exception as e:
                qlog.error(f"根据日期范围查询持仓失败: {e}")
                raise
        
        if session:
            return _get_by_date_range(session)
        else:
            with get_db_session() as db_session:
                return _get_by_date_range(db_session)
    
    def get_profitable_positions(self, skip: int = 0, limit: int = 100,
                                session: Optional[Session] = None) -> List[Position]:
        """
        获取盈利的持仓列表
        
        Args:
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            盈利持仓列表
        """
        def _get_profitable_positions(db_session: Session) -> List[Position]:
            try:
                result = (db_session.query(Position)
                         .filter(Position.total_pnl > 0)
                         .order_by(desc(Position.total_pnl))
                         .offset(skip)
                         .limit(limit)
                         .all())
                qlog.info(f"查询盈利持仓: 结果数量={len(result)}")
                return result
            except Exception as e:
                qlog.error(f"查询盈利持仓失败: {e}")
                raise
        
        if session:
            return _get_profitable_positions(session)
        else:
            with get_db_session() as db_session:
                return _get_profitable_positions(db_session)
    
    def get_losing_positions(self, skip: int = 0, limit: int = 100,
                            session: Optional[Session] = None) -> List[Position]:
        """
        获取亏损的持仓列表
        
        Args:
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            亏损持仓列表
        """
        def _get_losing_positions(db_session: Session) -> List[Position]:
            try:
                result = (db_session.query(Position)
                         .filter(Position.total_pnl < 0)
                         .order_by(asc(Position.total_pnl))
                         .offset(skip)
                         .limit(limit)
                         .all())
                qlog.info(f"查询亏损持仓: 结果数量={len(result)}")
                return result
            except Exception as e:
                qlog.error(f"查询亏损持仓失败: {e}")
                raise
        
        if session:
            return _get_losing_positions(session)
        else:
            with get_db_session() as db_session:
                return _get_losing_positions(db_session)
    
    def update_current_prices(self, price_updates: List[Dict[str, Any]],
                             session: Optional[Session] = None) -> int:
        """
        批量更新持仓的当前价格
        
        Args:
            price_updates: 价格更新列表，格式：[{'symbol': 'XAUUSD', 'price': 2650.50}, ...]
            session: 数据库会话
            
        Returns:
            更新的记录数量
        """
        def _update_prices(db_session: Session) -> int:
            try:
                updated_count = 0
                
                for update in price_updates:
                    symbol = update.get('symbol')
                    price = Decimal(str(update.get('price', 0)))
                    
                    if not symbol or price <= 0:
                        continue
                    
                    # 获取该合约的所有活跃持仓
                    positions = (db_session.query(Position)
                               .filter(and_(Position.symbol == symbol, 
                                          Position.is_active == True,
                                          Position.position_status == PositionStatus.OPEN))
                               .all())
                    
                    for position in positions:
                        position.update_current_price(price)
                        updated_count += 1
                
                db_session.flush()
                qlog.info(f"批量更新持仓价格成功: 数量={updated_count}")
                return updated_count

            except Exception as e:
                qlog.error(f"批量更新持仓价格失败: {e}")
                raise
        
        if session:
            return _update_prices(session)
        else:
            with get_db_session() as db_session:
                return _update_prices(db_session)

    def get_statistics_by_symbol(self, symbol: str, session: Optional[Session] = None) -> Dict[str, Any]:
        """
        获取指定合约的持仓统计信息

        Args:
            symbol: 合约代码
            session: 数据库会话

        Returns:
            统计信息字典
        """
        def _get_statistics(db_session: Session) -> Dict[str, Any]:
            try:
                # 基础统计
                total_count = db_session.query(func.count(Position.id)).filter(Position.symbol == symbol).scalar()

                if total_count == 0:
                    return {
                        'symbol': symbol,
                        'total_count': 0,
                        'active_count': 0,
                        'long_count': 0,
                        'short_count': 0,
                        'total_quantity': 0,
                        'total_cost': 0,
                        'total_market_value': 0,
                        'total_unrealized_pnl': 0,
                        'total_realized_pnl': 0,
                        'total_pnl': 0,
                        'total_commission': 0,
                        'avg_open_price': 0,
                        'profitable_count': 0,
                        'losing_count': 0
                    }

                # 活跃持仓统计
                active_count = db_session.query(func.count(Position.id)).filter(
                    and_(Position.symbol == symbol, Position.is_active == True)
                ).scalar()

                # 多空统计
                long_count = db_session.query(func.count(Position.id)).filter(
                    and_(Position.symbol == symbol, Position.position_side == PositionSide.LONG)
                ).scalar()
                short_count = db_session.query(func.count(Position.id)).filter(
                    and_(Position.symbol == symbol, Position.position_side == PositionSide.SHORT)
                ).scalar()

                # 数量和金额统计
                quantity_sum = db_session.query(func.sum(Position.total_quantity)).filter(
                    Position.symbol == symbol
                ).scalar() or 0

                cost_sum = db_session.query(func.sum(Position.position_cost)).filter(
                    Position.symbol == symbol
                ).scalar() or 0

                market_value_sum = db_session.query(func.sum(Position.market_value)).filter(
                    Position.symbol == symbol
                ).scalar() or 0

                unrealized_pnl_sum = db_session.query(func.sum(Position.unrealized_pnl)).filter(
                    Position.symbol == symbol
                ).scalar() or 0

                realized_pnl_sum = db_session.query(func.sum(Position.realized_pnl)).filter(
                    Position.symbol == symbol
                ).scalar() or 0

                total_pnl_sum = db_session.query(func.sum(Position.total_pnl)).filter(
                    Position.symbol == symbol
                ).scalar() or 0

                commission_sum = db_session.query(func.sum(Position.commission)).filter(
                    Position.symbol == symbol
                ).scalar() or 0

                # 平均开仓价格
                avg_open_price = db_session.query(func.avg(Position.open_price)).filter(
                    Position.symbol == symbol
                ).scalar() or 0

                # 盈亏统计
                profitable_count = db_session.query(func.count(Position.id)).filter(
                    and_(Position.symbol == symbol, Position.total_pnl > 0)
                ).scalar()

                losing_count = db_session.query(func.count(Position.id)).filter(
                    and_(Position.symbol == symbol, Position.total_pnl < 0)
                ).scalar()

                result = {
                    'symbol': symbol,
                    'total_count': total_count,
                    'active_count': active_count,
                    'long_count': long_count,
                    'short_count': short_count,
                    'total_quantity': float(quantity_sum),
                    'total_cost': float(cost_sum),
                    'total_market_value': float(market_value_sum),
                    'total_unrealized_pnl': float(unrealized_pnl_sum),
                    'total_realized_pnl': float(realized_pnl_sum),
                    'total_pnl': float(total_pnl_sum),
                    'total_commission': float(commission_sum),
                    'avg_open_price': float(avg_open_price),
                    'profitable_count': profitable_count,
                    'losing_count': losing_count
                }

                qlog.info(f"获取合约持仓统计信息: {symbol}, 结果={result}")
                return result

            except Exception as e:
                qlog.error(f"获取合约持仓统计信息失败: {e}")
                raise

        if session:
            return _get_statistics(session)
        else:
            with get_db_session() as db_session:
                return _get_statistics(db_session)

    def get_overall_statistics(self, session: Optional[Session] = None) -> Dict[str, Any]:
        """
        获取整体持仓统计信息

        Args:
            session: 数据库会话

        Returns:
            整体统计信息字典
        """
        def _get_overall_statistics(db_session: Session) -> Dict[str, Any]:
            try:
                # 基础统计
                total_count = db_session.query(func.count(Position.id)).scalar()
                active_count = db_session.query(func.count(Position.id)).filter(Position.is_active == True).scalar()

                # 多空统计
                long_count = db_session.query(func.count(Position.id)).filter(Position.position_side == PositionSide.LONG).scalar()
                short_count = db_session.query(func.count(Position.id)).filter(Position.position_side == PositionSide.SHORT).scalar()

                # 金额统计
                total_cost = db_session.query(func.sum(Position.position_cost)).scalar() or 0
                total_market_value = db_session.query(func.sum(Position.market_value)).scalar() or 0
                total_unrealized_pnl = db_session.query(func.sum(Position.unrealized_pnl)).scalar() or 0
                total_realized_pnl = db_session.query(func.sum(Position.realized_pnl)).scalar() or 0
                total_pnl = db_session.query(func.sum(Position.total_pnl)).scalar() or 0
                total_commission = db_session.query(func.sum(Position.commission)).scalar() or 0

                # 盈亏统计
                profitable_count = db_session.query(func.count(Position.id)).filter(Position.total_pnl > 0).scalar()
                losing_count = db_session.query(func.count(Position.id)).filter(Position.total_pnl < 0).scalar()

                # 合约统计
                symbol_count = db_session.query(func.count(func.distinct(Position.symbol))).scalar()

                result = {
                    'total_count': total_count,
                    'active_count': active_count,
                    'long_count': long_count,
                    'short_count': short_count,
                    'total_cost': float(total_cost),
                    'total_market_value': float(total_market_value),
                    'total_unrealized_pnl': float(total_unrealized_pnl),
                    'total_realized_pnl': float(total_realized_pnl),
                    'total_pnl': float(total_pnl),
                    'total_commission': float(total_commission),
                    'profitable_count': profitable_count,
                    'losing_count': losing_count,
                    'symbol_count': symbol_count,
                    'win_rate': (profitable_count / total_count * 100) if total_count > 0 else 0,
                    'total_return_rate': (total_pnl / total_cost * 100) if total_cost > 0 else 0
                }

                qlog.info(f"获取整体持仓统计信息: {result}")
                return result

            except Exception as e:
                qlog.error(f"获取整体持仓统计信息失败: {e}")
                raise

        if session:
            return _get_overall_statistics(session)
        else:
            with get_db_session() as db_session:
                return _get_overall_statistics(db_session)

    def search_positions(self, filters: Dict[str, Any], skip: int = 0, limit: int = 100,
                        order_by: str = 'open_time', desc_order: bool = True,
                        session: Optional[Session] = None) -> List[Position]:
        """
        复合条件搜索持仓

        Args:
            filters: 搜索条件字典，支持的键：
                - symbol: 合约代码
                - position_side: 持仓方向
                - position_status: 持仓状态
                - position_type: 持仓类型
                - channel_code: 渠道编号
                - is_active: 是否活跃
                - start_date: 开始日期
                - end_date: 结束日期
                - min_quantity: 最小持仓量
                - max_quantity: 最大持仓量
                - min_pnl: 最小盈亏
                - max_pnl: 最大盈亏
            skip: 跳过记录数
            limit: 限制记录数
            order_by: 排序字段
            desc_order: 是否降序
            session: 数据库会话

        Returns:
            持仓列表
        """
        def _search_positions(db_session: Session) -> List[Position]:
            try:
                query = db_session.query(Position)

                # 应用过滤条件
                if 'symbol' in filters:
                    query = query.filter(Position.symbol == filters['symbol'])

                if 'position_side' in filters:
                    query = query.filter(Position.position_side == filters['position_side'])

                if 'position_status' in filters:
                    query = query.filter(Position.position_status == filters['position_status'])

                if 'position_type' in filters:
                    query = query.filter(Position.position_type == filters['position_type'])

                if 'channel_code' in filters:
                    query = query.filter(Position.channel_code == filters['channel_code'])

                if 'is_active' in filters:
                    query = query.filter(Position.is_active == filters['is_active'])

                if 'start_date' in filters:
                    query = query.filter(Position.open_time >= filters['start_date'])

                if 'end_date' in filters:
                    query = query.filter(Position.open_time <= filters['end_date'])

                if 'min_quantity' in filters:
                    query = query.filter(Position.total_quantity >= filters['min_quantity'])

                if 'max_quantity' in filters:
                    query = query.filter(Position.total_quantity <= filters['max_quantity'])

                if 'min_pnl' in filters:
                    query = query.filter(Position.total_pnl >= filters['min_pnl'])

                if 'max_pnl' in filters:
                    query = query.filter(Position.total_pnl <= filters['max_pnl'])

                # 应用排序
                if hasattr(Position, order_by):
                    order_field = getattr(Position, order_by)
                    if desc_order:
                        query = query.order_by(desc(order_field))
                    else:
                        query = query.order_by(asc(order_field))

                # 应用分页
                result = query.offset(skip).limit(limit).all()

                qlog.info(f"复合条件搜索持仓: 条件={filters}, 结果数量={len(result)}")
                return result

            except Exception as e:
                qlog.error(f"复合条件搜索持仓失败: {e}")
                raise

        if session:
            return _search_positions(session)
        else:
            with get_db_session() as db_session:
                return _search_positions(db_session)

    def close_position(self, position_id: str, close_price: Decimal, close_order_id: str = None,
                      session: Optional[Session] = None) -> Optional[Position]:
        """
        关闭持仓

        Args:
            position_id: 持仓编号
            close_price: 平仓价格
            close_order_id: 平仓订单ID
            session: 数据库会话

        Returns:
            更新后的持仓实例
        """
        def _close_position(db_session: Session) -> Optional[Position]:
            try:
                position = db_session.query(Position).filter_by(position_id=position_id).first()

                if not position:
                    qlog.info(f"未找到持仓: {position_id}")
                    return None

                if position.position_status == PositionStatus.CLOSED:
                    qlog.info(f"持仓已关闭: {position_id}")
                    return position

                # 更新持仓状态
                position.position_status = PositionStatus.CLOSED
                position.close_time = datetime.now()
                position.close_price = close_price
                position.is_active = False

                if close_order_id:
                    position.close_order_id = close_order_id

                # 计算已实现盈亏
                price_diff = close_price - position.open_price
                if position.position_side == PositionSide.LONG:
                    realized_pnl = price_diff * position.total_quantity
                elif position.position_side == PositionSide.SHORT:
                    realized_pnl = -price_diff * position.total_quantity
                else:
                    realized_pnl = Decimal('0.00')

                position.realized_pnl = realized_pnl
                position.unrealized_pnl = Decimal('0.00')
                position.total_pnl = realized_pnl - position.commission - position.swap_fee

                db_session.flush()
                db_session.refresh(position)

                qlog.info(f"关闭持仓成功: {position_id}, 平仓价格: {close_price}, 已实现盈亏: {realized_pnl}")
                return position

            except Exception as e:
                qlog.error(f"关闭持仓失败: {e}")
                raise

        if session:
            return _close_position(session)
        else:
            with get_db_session() as db_session:
                return _close_position(db_session)


# 全局持仓DAO实例
position_dao = PositionDAO()


def get_position_dao() -> PositionDAO:
    """获取持仓DAO实例"""
    return position_dao
