"""
基础DAO类

提供通用的数据访问对象基类和常用操作
"""

from typing import TypeVar, Generic, List, Optional, Dict, Any, Type, Union
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, or_, desc, asc, func
from datetime import datetime

from custom_api.database.connection import get_db_session
from custom_api import qlog

# 泛型类型变量
ModelType = TypeVar('ModelType')
CreateSchemaType = TypeVar('CreateSchemaType')
UpdateSchemaType = TypeVar('UpdateSchemaType')


class BaseDAO(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """
    基础DAO类
    
    提供通用的CRUD操作和查询方法
    """
    
    def __init__(self, model: Type[ModelType]):
        """
        初始化DAO
        
        Args:
            model: SQLAlchemy模型类
        """
        self.model = model
    
    def create(self, obj_in: Union[CreateSchemaType, Dict[str, Any]], 
               session: Optional[Session] = None) -> ModelType:
        """
        创建新记录
        
        Args:
            obj_in: 创建数据
            session: 数据库会话
            
        Returns:
            创建的模型实例
        """
        def _create(db_session: Session) -> ModelType:
            try:
                if isinstance(obj_in, dict):
                    db_obj = self.model(**obj_in)
                else:
                    obj_data = obj_in.dict() if hasattr(obj_in, 'dict') else obj_in.__dict__
                    db_obj = self.model(**obj_data)
                
                db_session.add(db_obj)
                db_session.flush()
                db_session.refresh(db_obj)
                
                qlog.info(f"创建{self.model.__name__}记录成功: {db_obj}")
                return db_obj

            except SQLAlchemyError as e:
                qlog.error(f"创建{self.model.__name__}记录失败: {e}")
                raise
        
        if session:
            return _create(session)
        else:
            with get_db_session() as db_session:
                return _create(db_session)
    
    def get(self, id: Any, session: Optional[Session] = None) -> Optional[ModelType]:
        """
        根据ID获取记录
        
        Args:
            id: 记录ID
            session: 数据库会话
            
        Returns:
            模型实例或None
        """
        def _get(db_session: Session) -> Optional[ModelType]:
            try:
                result = db_session.query(self.model).filter(self.model.id == id).first()
                qlog.info(f"查询{self.model.__name__}记录: ID={id}, 结果={'找到' if result else '未找到'}")
                return result
            except SQLAlchemyError as e:
                qlog.error(f"查询{self.model.__name__}记录失败: {e}")
                raise
        
        if session:
            return _get(session)
        else:
            with get_db_session() as db_session:
                return _get(db_session)
    
    def get_by_field(self, field_name: str, field_value: Any, 
                     session: Optional[Session] = None) -> Optional[ModelType]:
        """
        根据字段值获取记录
        
        Args:
            field_name: 字段名
            field_value: 字段值
            session: 数据库会话
            
        Returns:
            模型实例或None
        """
        def _get_by_field(db_session: Session) -> Optional[ModelType]:
            try:
                field = getattr(self.model, field_name)
                result = db_session.query(self.model).filter(field == field_value).first()
                qlog.info(f"查询{self.model.__name__}记录: {field_name}={field_value}, 结果={'找到' if result else '未找到'}")
                return result
            except AttributeError:
                qlog.error(f"{self.model.__name__}模型没有字段: {field_name}")
                raise
            except SQLAlchemyError as e:
                qlog.error(f"查询{self.model.__name__}记录失败: {e}")
                raise
        
        if session:
            return _get_by_field(session)
        else:
            with get_db_session() as db_session:
                return _get_by_field(db_session)
    
    def get_multi(self, skip: int = 0, limit: int = 100, 
                  session: Optional[Session] = None) -> List[ModelType]:
        """
        获取多条记录
        
        Args:
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            模型实例列表
        """
        def _get_multi(db_session: Session) -> List[ModelType]:
            try:
                result = db_session.query(self.model).offset(skip).limit(limit).all()
                qlog.info(f"查询{self.model.__name__}记录列表: skip={skip}, limit={limit}, 结果数量={len(result)}")
                return result
            except SQLAlchemyError as e:
                qlog.error(f"查询{self.model.__name__}记录列表失败: {e}")
                raise
        
        if session:
            return _get_multi(session)
        else:
            with get_db_session() as db_session:
                return _get_multi(db_session)
    
    def update(self, db_obj: ModelType, obj_in: Union[UpdateSchemaType, Dict[str, Any]], 
               session: Optional[Session] = None) -> ModelType:
        """
        更新记录
        
        Args:
            db_obj: 数据库对象
            obj_in: 更新数据
            session: 数据库会话
            
        Returns:
            更新后的模型实例
        """
        def _update(db_session: Session) -> ModelType:
            try:
                if isinstance(obj_in, dict):
                    update_data = obj_in
                else:
                    update_data = obj_in.dict(exclude_unset=True) if hasattr(obj_in, 'dict') else obj_in.__dict__
                
                for field, value in update_data.items():
                    if hasattr(db_obj, field):
                        setattr(db_obj, field, value)
                
                # 更新版本号（如果存在）
                if hasattr(db_obj, 'version'):
                    db_obj.version += 1
                
                # 更新时间（如果存在）
                if hasattr(db_obj, 'update_time'):
                    db_obj.update_time = datetime.now()
                
                db_session.flush()
                db_session.refresh(db_obj)
                
                qlog.info(f"更新{self.model.__name__}记录成功: {db_obj}")
                return db_obj

            except SQLAlchemyError as e:
                qlog.error(f"更新{self.model.__name__}记录失败: {e}")
                raise
        
        if session:
            return _update(session)
        else:
            with get_db_session() as db_session:
                return _update(db_session)
    
    def delete(self, id: Any, session: Optional[Session] = None) -> bool:
        """
        删除记录
        
        Args:
            id: 记录ID
            session: 数据库会话
            
        Returns:
            是否删除成功
        """
        def _delete(db_session: Session) -> bool:
            try:
                obj = db_session.query(self.model).filter(self.model.id == id).first()
                if obj:
                    db_session.delete(obj)
                    db_session.flush()
                    qlog.info(f"删除{self.model.__name__}记录成功: ID={id}")
                    return True
                else:
                    qlog.info(f"删除{self.model.__name__}记录失败: 记录不存在, ID={id}")
                    return False
            except SQLAlchemyError as e:
                qlog.error(f"删除{self.model.__name__}记录失败: {e}")
                raise
        
        if session:
            return _delete(session)
        else:
            with get_db_session() as db_session:
                return _delete(db_session)
    
    def count(self, session: Optional[Session] = None) -> int:
        """
        获取记录总数
        
        Args:
            session: 数据库会话
            
        Returns:
            记录总数
        """
        def _count(db_session: Session) -> int:
            try:
                result = db_session.query(func.count(self.model.id)).scalar()
                qlog.info(f"统计{self.model.__name__}记录数量: {result}")
                return result
            except SQLAlchemyError as e:
                qlog.error(f"统计{self.model.__name__}记录数量失败: {e}")
                raise
        
        if session:
            return _count(session)
        else:
            with get_db_session() as db_session:
                return _count(db_session)
    
    def exists(self, id: Any, session: Optional[Session] = None) -> bool:
        """
        检查记录是否存在
        
        Args:
            id: 记录ID
            session: 数据库会话
            
        Returns:
            是否存在
        """
        def _exists(db_session: Session) -> bool:
            try:
                result = db_session.query(self.model.id).filter(self.model.id == id).first() is not None
                qlog.info(f"检查{self.model.__name__}记录是否存在: ID={id}, 结果={result}")
                return result
            except SQLAlchemyError as e:
                qlog.error(f"检查{self.model.__name__}记录是否存在失败: {e}")
                raise
        
        if session:
            return _exists(session)
        else:
            with get_db_session() as db_session:
                return _exists(db_session)
    
    def filter_by(self, filters: Dict[str, Any], skip: int = 0, limit: int = 100,
                  order_by: Optional[str] = None, desc_order: bool = False,
                  session: Optional[Session] = None) -> List[ModelType]:
        """
        根据条件过滤记录
        
        Args:
            filters: 过滤条件字典
            skip: 跳过记录数
            limit: 限制记录数
            order_by: 排序字段
            desc_order: 是否降序
            session: 数据库会话
            
        Returns:
            模型实例列表
        """
        def _filter_by(db_session: Session) -> List[ModelType]:
            try:
                query = db_session.query(self.model)
                
                # 应用过滤条件
                for field_name, field_value in filters.items():
                    if hasattr(self.model, field_name):
                        field = getattr(self.model, field_name)
                        query = query.filter(field == field_value)
                
                # 应用排序
                if order_by and hasattr(self.model, order_by):
                    order_field = getattr(self.model, order_by)
                    if desc_order:
                        query = query.order_by(desc(order_field))
                    else:
                        query = query.order_by(asc(order_field))
                
                # 应用分页
                result = query.offset(skip).limit(limit).all()
                
                qlog.info(f"过滤查询{self.model.__name__}记录: 条件={filters}, 结果数量={len(result)}")
                return result

            except SQLAlchemyError as e:
                qlog.error(f"过滤查询{self.model.__name__}记录失败: {e}")
                raise
        
        if session:
            return _filter_by(session)
        else:
            with get_db_session() as db_session:
                return _filter_by(db_session)

    def batch_create(self, objs_in: List[Union[CreateSchemaType, Dict[str, Any]]],
                     session: Optional[Session] = None) -> List[ModelType]:
        """
        批量创建记录

        Args:
            objs_in: 创建数据列表
            session: 数据库会话

        Returns:
            创建的模型实例列表
        """
        def _batch_create(db_session: Session) -> List[ModelType]:
            try:
                db_objs = []
                for obj_in in objs_in:
                    if isinstance(obj_in, dict):
                        db_obj = self.model(**obj_in)
                    else:
                        obj_data = obj_in.dict() if hasattr(obj_in, 'dict') else obj_in.__dict__
                        db_obj = self.model(**obj_data)
                    db_objs.append(db_obj)

                db_session.add_all(db_objs)
                db_session.flush()

                for db_obj in db_objs:
                    db_session.refresh(db_obj)

                qlog.info(f"批量创建{self.model.__name__}记录成功: 数量={len(db_objs)}")
                return db_objs

            except SQLAlchemyError as e:
                qlog.error(f"批量创建{self.model.__name__}记录失败: {e}")
                raise

        if session:
            return _batch_create(session)
        else:
            with get_db_session() as db_session:
                return _batch_create(db_session)

    def batch_update(self, updates: List[Dict[str, Any]],
                     session: Optional[Session] = None) -> int:
        """
        批量更新记录

        Args:
            updates: 更新数据列表，每个字典必须包含id字段
            session: 数据库会话

        Returns:
            更新的记录数量
        """
        def _batch_update(db_session: Session) -> int:
            try:
                updated_count = 0
                for update_data in updates:
                    if 'id' not in update_data:
                        continue

                    obj_id = update_data.pop('id')
                    db_obj = db_session.query(self.model).filter(self.model.id == obj_id).first()

                    if db_obj:
                        for field, value in update_data.items():
                            if hasattr(db_obj, field):
                                setattr(db_obj, field, value)

                        # 更新版本号和时间
                        if hasattr(db_obj, 'version'):
                            db_obj.version += 1
                        if hasattr(db_obj, 'update_time'):
                            db_obj.update_time = datetime.now()

                        updated_count += 1

                db_session.flush()
                qlog.info(f"批量更新{self.model.__name__}记录成功: 数量={updated_count}")
                return updated_count

            except SQLAlchemyError as e:
                qlog.error(f"批量更新{self.model.__name__}记录失败: {e}")
                raise

        if session:
            return _batch_update(session)
        else:
            with get_db_session() as db_session:
                return _batch_update(db_session)
