import logging
import os
from pathlib import Path
from typing import Iterator, List, Optional

import numpy as np
import pandas as pd


class FXTReader:
    """
    FXT 文件读取器（基于内存映射），支持快速将 .fxt 文件解析为 DataFrame 或增量生成器。

    默认结构（56字节/条，含4字节对齐padding）：
        - int32   CurrentBarTime      (4B)
        - void4   padding 对齐         (4B)
        - float64 Open                (8B)
        - float64 Low                 (8B)
        - float64 High                (8B)
        - float64 Close               (8B)
        - float64 Volume (或 int64)    (8B)
        - int32   IncomingTickTime    (4B)
        - int32   Flag                (4B)
    合计 56 字节/条

    参数说明见 __init__。
    """

    def __init__(
        self,
        file_path: str | Path,
        header_bytes: int = 728,
        align_with_padding: bool = True,
        volume_is_integer: bool = False,
        endian: str = "<",
        logger: Optional[logging.Logger] = None,
    ) -> None:
        self.file_path = Path(file_path)
        self.header_bytes = int(header_bytes)
        self.align_with_padding = bool(align_with_padding)
        self.volume_is_integer = bool(volume_is_integer)
        self.endian = endian
        self.logger = logger or logging.getLogger(__name__)

        if self.endian not in ("<", ">"):
            raise ValueError("endian 必须是 '<' 或 '>'")

        self.dtype = self._build_np_dtype()

    def _build_np_dtype(self) -> np.dtype:
        i4 = f"{self.endian}i4"
        f8 = f"{self.endian}f8"
        vol_dt = f"{self.endian}{'i8' if self.volume_is_integer else 'f8'}"

        if self.align_with_padding:
            fields = [
                ("CurrentBarTime", i4),
                ("_pad0", "V4"),
                ("Open", f8),
                ("Low", f8),
                ("High", f8),
                ("Close", f8),
                ("Volume", vol_dt),
                ("IncomingTickTime", i4),
                ("Flag", i4),
            ]
        else:
            fields = [
                ("CurrentBarTime", i4),
                ("Open", f8),
                ("Low", f8),
                ("High", f8),
                ("Close", f8),
                ("Volume", vol_dt),
                ("IncomingTickTime", i4),
                ("Flag", i4),
            ]
        return np.dtype(fields, align=False)

    def _map(self) -> np.memmap:
        if not self.file_path.exists() or not self.file_path.is_file():
            raise FileNotFoundError(f"FXT文件不存在: {self.file_path}")
        file_size = self.file_path.stat().st_size
        if file_size <= self.header_bytes:
            raise ValueError(
                f"文件过小（{file_size}B），小于或等于头大小 {self.header_bytes}B"
            )

        payload_size = file_size - self.header_bytes
        itemsize = self.dtype.itemsize
        if payload_size < itemsize:
            raise ValueError(
                f"有效载荷 {payload_size}B 小于单条记录长度 {itemsize}B，无法解析"
            )

        num_records = payload_size // itemsize
        remainder = payload_size % itemsize
        if remainder != 0:
            self.logger.warning(
                "文件末尾有残余字节未对齐，将被忽略: payload=%d, itemsize=%d, remainder=%d",
                payload_size,
                itemsize,
                remainder,
            )

        arr = np.memmap(
            str(self.file_path),
            mode="r",
            dtype=self.dtype,
            offset=self.header_bytes,
            shape=(num_records,),
            order="C",
        )
        self.logger.info("FXT已映射: 记录数=%d, 每条字节=%d", num_records, itemsize)
        return arr

    def to_dataframe(self, convert_time_to_ms: bool = True) -> pd.DataFrame:
        arr = self._map()
        try:
            df = pd.DataFrame(
                {
                    "CurrentBarTime": arr["CurrentBarTime"].astype("int64", copy=False),
                    "Open": arr["Open"].astype("float64", copy=False),
                    "Low": arr["Low"].astype("float64", copy=False),
                    "High": arr["High"].astype("float64", copy=False),
                    "Close": arr["Close"].astype("float64", copy=False),
                    "Volume": (
                        arr["Volume"].astype("int64", copy=False)
                        if self.volume_is_integer
                        else arr["Volume"].astype("float64", copy=False)
                    ),
                    "IncomingTickTime": arr["IncomingTickTime"].astype("int64", copy=False),
                    "Flag": arr["Flag"].astype("int32", copy=False),
                }
            )
            if convert_time_to_ms:
                # 原始字段多为秒级时间戳，这里转换为毫秒便于统一
                for col in ("CurrentBarTime", "IncomingTickTime"):
                    df[col] = df[col] * 1000
            return df
        finally:
            del arr

    def iter_bars(self, symbol: str, source: str, price_field: str = "Close") -> Iterator[dict]:
        """
        以生成器方式逐条输出K线bar（按文件顺序）。

        返回元素字段：time, open, high, low, close, volume, symbol, source
        """
        df = self.to_dataframe(convert_time_to_ms=True)
        cols = set(df.columns)
        required = {"CurrentBarTime", "Open", "High", "Low", "Close", "Volume"}
        missing = required - cols
        if missing:
            raise ValueError(f"FXT缺少必要列: {missing}")

        for _, row in df.iterrows():
            yield {
                "time": int(row["CurrentBarTime"]),
                "open": float(row["Open"]),
                "high": float(row["High"]),
                "low": float(row["Low"]),
                "close": float(row[price_field if price_field in ("Open", "High", "Low", "Close") else "Close"]),
                "volume": int(row["Volume"]) if self.volume_is_integer else float(row["Volume"]),
                "symbol": symbol,
                "source": source,
            }

    @staticmethod
    def infer_timeframe_minutes_from_filename(file_path: str | Path) -> Optional[int]:
        """
        从文件名中推断时间周期（分钟），例如 XAUUSD60_0.fxt -> 60。
        若无法推断返回 None。
        """
        name = Path(file_path).name
        # 简单提取连续数字作为周期
        digits = "".join([ch for ch in name if ch.isdigit()])
        if not digits:
            return None
        try:
            return int(digits)
        except Exception:
            return None 