#!/usr/bin/env python3
"""
开发环境启动脚本
"""
import os
import sys
import subprocess
import time
import signal
from pathlib import Path

def run_backend():
    """启动后端服务"""
    backend_dir = Path(__file__).parent / "backend"
    os.chdir(backend_dir)
    
    print("启动后端服务...")
    return subprocess.Popen([
        sys.executable, "-m", "uvicorn", "main:app", 
        "--reload", "--host", "0.0.0.0", "--port", "8000"
    ])

def run_frontend():
    """启动前端服务"""
    frontend_dir = Path(__file__).parent / "frontend"
    os.chdir(frontend_dir)
    
    print("启动前端服务...")
    return subprocess.Popen(["npm", "run", "dev"])

def main():
    """主函数"""
    processes = []
    
    try:
        # 启动后端
        backend_process = run_backend()
        processes.append(backend_process)
        time.sleep(3)  # 等待后端启动
        
        # 启动前端
        frontend_process = run_frontend()
        processes.append(frontend_process)
        
        print("\n" + "="*50)
        print("开发服务已启动:")
        print("- 后端API: http://localhost:8000")
        print("- API文档: http://localhost:8000/docs")
        print("- 前端界面: http://localhost:3000")
        print("="*50)
        print("\n按 Ctrl+C 停止所有服务")
        
        # 等待进程
        for process in processes:
            process.wait()
            
    except KeyboardInterrupt:
        print("\n正在停止服务...")
        
        # 终止所有进程
        for process in processes:
            if process.poll() is None:
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
        
        print("所有服务已停止")

if __name__ == "__main__":
    main()
