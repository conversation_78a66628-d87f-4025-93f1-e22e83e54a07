#!/usr/bin/env python3
"""
启动后端服务的简单脚本
"""
import sys
import os
from pathlib import Path

# 添加backend目录到Python路径
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

# 切换到backend目录
os.chdir(backend_path)

if __name__ == "__main__":
    import uvicorn

    print("启动股票回放系统后端服务...")
    print("API文档地址: http://localhost:8000/docs")
    print("WebSocket地址: ws://localhost:8000/ws/replay")
    print("按 Ctrl+C 停止服务")

    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
