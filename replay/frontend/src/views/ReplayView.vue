<template>
  <div class="replay-view">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="symbol-selector">
        <el-select
          v-model="selectedSymbol"
          placeholder="选择交易品种"
          @change="onSymbolChange"
          style="width: 150px"
        >
          <el-option
            v-for="symbol in availableSymbols"
            :key="symbol"
            :label="symbol"
            :value="symbol"
          />
        </el-select>
      </div>
      
      <div class="period-selector">
        <el-select
          v-model="selectedPeriod"
          @change="onPeriodChange"
          style="width: 100px"
        >
          <el-option
            v-for="period in supportedPeriods"
            :key="period"
            :label="period"
            :value="period"
          />
        </el-select>
      </div>
      
      <el-button
        type="primary"
        @click="loadData"
        :loading="isLoading"
        :disabled="!selectedSymbol"
      >
        加载数据
      </el-button>
    </div>
    
    <!-- 图表区域 -->
    <div class="chart-container">
      <KLineChart ref="chartRef" />
    </div>
    
    <!-- 播放控制区域 -->
    <div class="control-container">
      <PlaybackControl />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useReplayStore } from '../stores/replay'
import KLineChart from '../components/KLineChart.vue'
import PlaybackControl from '../components/PlaybackControl.vue'
import { apiService } from '../services/api'
import { websocketService } from '../services/websocket'

const replayStore = useReplayStore()
const chartRef = ref(null)

// 响应式数据
const availableSymbols = ref([])
const supportedPeriods = ref(['1m', '5m', '15m', '30m', '1h', '4h', '1d'])
const selectedSymbol = ref('')
const selectedPeriod = ref('1m')

// 计算属性
const isLoading = computed(() => replayStore.isLoading)

// 方法
const loadAvailableSymbols = async () => {
  try {
    const response = await apiService.getAvailableSymbols()
    availableSymbols.value = response.symbols
    
    // 如果有可用品种，默认选择第一个
    if (availableSymbols.value.length > 0 && !selectedSymbol.value) {
      selectedSymbol.value = availableSymbols.value[0]
    }
  } catch (error) {
    ElMessage.error('获取可用品种失败: ' + error.message)
  }
}

const loadSupportedPeriods = async () => {
  try {
    const response = await apiService.getSupportedPeriods()
    supportedPeriods.value = response.periods
    selectedPeriod.value = response.default_period
  } catch (error) {
    ElMessage.error('获取支持的时间周期失败: ' + error.message)
  }
}

const loadData = async () => {
  if (!selectedSymbol.value) {
    ElMessage.warning('请选择交易品种')
    return
  }
  
  try {
    replayStore.setLoading(true)
    replayStore.clearError()
    
    // 通过WebSocket加载数据
    await websocketService.loadData(selectedSymbol.value, selectedPeriod.value)
    
    // 更新store中的品种和周期
    replayStore.setSymbol(selectedSymbol.value)
    replayStore.setPeriod(selectedPeriod.value)
    
    ElMessage.success('数据加载成功')
  } catch (error) {
    ElMessage.error('加载数据失败: ' + error.message)
    replayStore.setError(error.message)
  } finally {
    replayStore.setLoading(false)
  }
}

const onSymbolChange = (value) => {
  selectedSymbol.value = value
}

const onPeriodChange = (value) => {
  selectedPeriod.value = value
}

// 生命周期
onMounted(async () => {
  // 初始化WebSocket连接
  websocketService.connect()
  
  // 加载基础数据
  await Promise.all([
    loadAvailableSymbols(),
    loadSupportedPeriods()
  ])
})
</script>

<style scoped>
.replay-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-container {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.control-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 16px;
}
</style>
