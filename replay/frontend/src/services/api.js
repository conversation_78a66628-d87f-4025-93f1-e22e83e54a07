/**
 * HTTP API服务
 */
import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    const message = error.response?.data?.detail || error.message || '请求失败'
    console.error('API请求错误:', message)
    ElMessage.error(message)
    return Promise.reject(new Error(message))
  }
)

/**
 * API服务类
 */
class ApiService {
  /**
   * 获取可用的交易品种
   */
  async getAvailableSymbols() {
    return await api.get('/data/symbols')
  }

  /**
   * 获取支持的时间周期
   */
  async getSupportedPeriods() {
    return await api.get('/data/get_periods')
  }

  /**
   * 加载历史数据
   */
  async loadHistoricalData(params) {
    return await api.get('/data/load_data', { params })
  }

  /**
   * 获取数据时间范围
   */
  async getDataRange(symbol) {
    return await api.get(`/data/data_range/${symbol}`)
  }

  /**
   * 获取回放状态
   */
  async getReplayStatus() {
    return await api.get('/replay/status')
  }

  /**
   * 控制回放
   */
  async controlReplay(control) {
    return await api.post('/replay/control', control)
  }

  /**
   * 获取回放配置
   */
  async getReplayConfig() {
    return await api.get('/replay/config')
  }

  /**
   * 重置回放
   */
  async resetReplay() {
    return await api.post('/replay/reset')
  }

  /**
   * 健康检查
   */
  async healthCheck() {
    return await api.get('/health')
  }

  /**
   * 数据服务健康检查
   */
  async dataHealthCheck() {
    return await api.get('/data/health')
  }
}

// 创建全局实例
export const apiService = new ApiService()
