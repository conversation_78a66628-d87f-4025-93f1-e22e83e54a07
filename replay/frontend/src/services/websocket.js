/**
 * WebSocket服务 - 处理与后端的实时通信
 */
import { ElMessage } from 'element-plus'
import { useReplayStore } from '../stores/replay'

class WebSocketService {
  constructor() {
    this.ws = null
    this.reconnectTimer = null
    this.heartbeatTimer = null
    this.isConnecting = false
    this.shouldReconnect = true
    this.replayStore = null
  }

  /**
   * 连接WebSocket
   */
  connect() {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return
    }

    this.isConnecting = true
    this.replayStore = useReplayStore()

    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const wsUrl = `${protocol}//${window.location.host}/ws/replay`

    try {
      this.ws = new WebSocket(wsUrl)
      this.setupEventHandlers()
    } catch (error) {
      console.error('WebSocket连接失败:', error)
      this.handleConnectionError()
    }
  }

  /**
   * 设置事件处理器
   */
  setupEventHandlers() {
    this.ws.onopen = () => {
      console.log('WebSocket连接已建立')
      this.isConnecting = false
      this.replayStore.setConnected(true)
      this.replayStore.resetReconnectAttempts()
      this.startHeartbeat()
      ElMessage.success('连接服务器成功')
    }

    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data)
        this.handleMessage(message)
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
      }
    }

    this.ws.onclose = (event) => {
      console.log('WebSocket连接已关闭:', event.code, event.reason)
      this.isConnecting = false
      this.replayStore.setConnected(false)
      this.stopHeartbeat()
      
      if (this.shouldReconnect && this.replayStore.reconnectAttempts < this.replayStore.maxReconnectAttempts) {
        this.scheduleReconnect()
      } else {
        ElMessage.error('与服务器连接断开')
      }
    }

    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error)
      this.handleConnectionError()
    }
  }

  /**
   * 处理接收到的消息
   */
  handleMessage(message) {
    switch (message.type) {
      case 'kline_update':
        this.handleKlineUpdate(message.data)
        break
      case 'status_update':
        this.handleStatusUpdate(message.data)
        break
      case 'error':
        this.handleError(message.data)
        break
      case 'heartbeat':
        this.handleHeartbeat()
        break
      default:
        console.warn('未知的消息类型:', message.type)
    }
  }

  /**
   * 处理K线数据更新
   */
  handleKlineUpdate(data) {
    this.replayStore.updateKlineData(data)
  }

  /**
   * 处理状态更新
   */
  handleStatusUpdate(data) {
    this.replayStore.updateReplayStatus(data)
  }

  /**
   * 处理错误消息
   */
  handleError(data) {
    console.error('服务器错误:', data)
    ElMessage.error(data.message || '服务器错误')
    this.replayStore.setError(data.message || '未知错误')
  }

  /**
   * 处理心跳
   */
  handleHeartbeat() {
    // 心跳响应，保持连接活跃
  }

  /**
   * 发送消息到服务器
   */
  sendMessage(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      throw new Error('WebSocket连接未建立')
    }
  }

  /**
   * 播放
   */
  async play() {
    this.sendMessage({
      action: 'play',
      payload: {}
    })
  }

  /**
   * 暂停
   */
  async pause() {
    this.sendMessage({
      action: 'pause',
      payload: {}
    })
  }

  /**
   * 跳转到指定时间
   */
  async seek(timestamp) {
    this.sendMessage({
      action: 'seek',
      payload: { timestamp }
    })
  }

  /**
   * 设置播放速度
   */
  async setSpeed(speed) {
    this.sendMessage({
      action: 'speed',
      payload: { speed }
    })
  }

  /**
   * 加载数据
   */
  async loadData(symbol, period) {
    this.replayStore.clearKlineData()
    this.sendMessage({
      action: 'load',
      payload: { symbol, period }
    })
  }

  /**
   * 发送心跳
   */
  sendHeartbeat() {
    this.sendMessage({
      action: 'heartbeat',
      payload: {}
    })
  }

  /**
   * 开始心跳
   */
  startHeartbeat() {
    this.stopHeartbeat()
    this.heartbeatTimer = setInterval(() => {
      this.sendHeartbeat()
    }, 30000) // 30秒心跳间隔
  }

  /**
   * 停止心跳
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 处理连接错误
   */
  handleConnectionError() {
    this.isConnecting = false
    this.replayStore.setConnected(false)
    this.replayStore.incrementReconnectAttempts()
    
    if (this.shouldReconnect && this.replayStore.reconnectAttempts < this.replayStore.maxReconnectAttempts) {
      this.scheduleReconnect()
    }
  }

  /**
   * 安排重连
   */
  scheduleReconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }

    const delay = Math.min(1000 * Math.pow(2, this.replayStore.reconnectAttempts), 30000)
    console.log(`${delay}ms后尝试重连...`)

    this.reconnectTimer = setTimeout(() => {
      if (this.shouldReconnect) {
        console.log('尝试重连WebSocket...')
        this.connect()
      }
    }, delay)
  }

  /**
   * 断开连接
   */
  disconnect() {
    this.shouldReconnect = false
    this.stopHeartbeat()
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    if (this.ws) {
      this.ws.close()
      this.ws = null
    }

    if (this.replayStore) {
      this.replayStore.setConnected(false)
    }
  }

  /**
   * 获取连接状态
   */
  isConnected() {
    return this.ws && this.ws.readyState === WebSocket.OPEN
  }
}

// 创建全局实例
export const websocketService = new WebSocketService()
