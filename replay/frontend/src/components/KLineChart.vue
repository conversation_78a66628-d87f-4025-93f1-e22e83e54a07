<template>
  <div class="kline-chart">
    <div class="chart-header">
      <div class="chart-info">
        <span class="symbol">{{ symbol }}</span>
        <span class="period">{{ period }}</span>
        <span class="current-time">{{ formattedCurrentTime }}</span>
      </div>
      <div class="chart-controls">
        <el-button-group>
          <el-button size="small" @click="resetZoom">重置缩放</el-button>
          <el-button size="small" @click="fitContent">适应内容</el-button>
        </el-button-group>
      </div>
    </div>
    
    <div 
      ref="chartContainer" 
      class="chart-content"
      :style="{ height: chartHeight + 'px' }"
    ></div>
    
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <el-loading-spinner />
      <span>加载中...</span>
    </div>
    
    <!-- 无数据状态 -->
    <div v-if="!isLoading && klineData.length === 0" class="no-data">
      <el-empty description="暂无数据" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { createChart } from 'lightweight-charts'
import { useReplayStore } from '../stores/replay'

const replayStore = useReplayStore()

// 响应式数据
const chartContainer = ref(null)
const chartHeight = ref(400)
let chart = null
let candlestickSeries = null
let volumeSeries = null

// 计算属性
const symbol = computed(() => replayStore.symbol)
const period = computed(() => replayStore.period)
const formattedCurrentTime = computed(() => replayStore.formattedCurrentTime)
const isLoading = computed(() => replayStore.isLoading)
const klineData = computed(() => replayStore.klineData)
const currentTime = computed(() => replayStore.currentTime)

// 图表配置
const chartOptions = {
  layout: {
    background: { color: '#ffffff' },
    textColor: '#333333',
  },
  grid: {
    vertLines: { color: '#f0f0f0' },
    horzLines: { color: '#f0f0f0' },
  },
  crosshair: {
    mode: 1, // 十字光标模式
  },
  rightPriceScale: {
    borderColor: '#cccccc',
  },
  timeScale: {
    borderColor: '#cccccc',
    timeVisible: true,
    secondsVisible: false,
  },
  watermark: {
    visible: true,
    fontSize: 24,
    horzAlign: 'center',
    vertAlign: 'center',
    color: 'rgba(171, 71, 188, 0.3)',
    text: '股票回放系统',
  },
}

const candlestickOptions = {
  upColor: '#26a69a',
  downColor: '#ef5350',
  borderVisible: false,
  wickUpColor: '#26a69a',
  wickDownColor: '#ef5350',
}

const volumeOptions = {
  color: '#26a69a',
  priceFormat: {
    type: 'volume',
  },
  priceScaleId: '',
  scaleMargins: {
    top: 0.8,
    bottom: 0,
  },
}

// 方法
const initChart = () => {
  if (!chartContainer.value) return
  
  // 创建图表
  chart = createChart(chartContainer.value, {
    ...chartOptions,
    width: chartContainer.value.clientWidth,
    height: chartHeight.value,
  })
  
  // 创建K线系列
  candlestickSeries = chart.addCandlestickSeries(candlestickOptions)
  
  // 创建成交量系列
  volumeSeries = chart.addHistogramSeries(volumeOptions)
  
  // 监听图表事件
  chart.subscribeCrosshairMove((param) => {
    // 可以在这里处理十字光标移动事件
  })
  
  // 监听可见范围变化
  chart.timeScale().subscribeVisibleTimeRangeChange((timeRange) => {
    // 可以在这里处理时间范围变化事件
  })
}

const updateChart = () => {
  if (!candlestickSeries || !volumeSeries) return
  
  // 转换数据格式
  const candleData = klineData.value.map(item => ({
    time: Math.floor(item.time / 1000), // lightweight-charts使用秒级时间戳
    open: item.open,
    high: item.high,
    low: item.low,
    close: item.close,
  }))
  
  const volumeData = klineData.value.map(item => ({
    time: Math.floor(item.time / 1000),
    value: item.volume,
    color: item.close >= item.open ? '#26a69a' : '#ef5350',
  }))
  
  // 设置数据
  candlestickSeries.setData(candleData)
  volumeSeries.setData(volumeData)
}

const addNewBar = (newData) => {
  if (!candlestickSeries || !volumeSeries) return
  
  const candleBar = {
    time: Math.floor(newData.time / 1000),
    open: newData.open,
    high: newData.high,
    low: newData.low,
    close: newData.close,
  }
  
  const volumeBar = {
    time: Math.floor(newData.time / 1000),
    value: newData.volume,
    color: newData.close >= newData.open ? '#26a69a' : '#ef5350',
  }
  
  // 更新数据
  candlestickSeries.update(candleBar)
  volumeSeries.update(volumeBar)
}

const resetZoom = () => {
  if (chart) {
    chart.timeScale().resetTimeScale()
  }
}

const fitContent = () => {
  if (chart) {
    chart.timeScale().fitContent()
  }
}

const resizeChart = () => {
  if (chart && chartContainer.value) {
    chart.applyOptions({
      width: chartContainer.value.clientWidth,
      height: chartHeight.value,
    })
  }
}

// 监听数据变化
watch(klineData, (newData) => {
  if (newData.length > 0) {
    updateChart()
  }
}, { deep: true })

// 监听新数据添加
watch(() => replayStore.klineData.length, (newLength, oldLength) => {
  if (newLength > oldLength && newLength > 0) {
    const newData = replayStore.klineData[newLength - 1]
    addNewBar(newData)
  }
})

// 生命周期
onMounted(async () => {
  await nextTick()
  initChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', resizeChart)
})

onUnmounted(() => {
  window.removeEventListener('resize', resizeChart)
  
  if (chart) {
    chart.remove()
    chart = null
  }
})

// 暴露方法给父组件
defineExpose({
  resetZoom,
  fitContent,
  resizeChart
})
</script>

<style scoped>
.kline-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.chart-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.symbol {
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
}

.period {
  font-size: 14px;
  color: #909399;
  background: #f0f9ff;
  padding: 4px 8px;
  border-radius: 4px;
}

.current-time {
  font-size: 14px;
  color: #606266;
}

.chart-content {
  flex: 1;
  position: relative;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 12px;
  z-index: 1000;
}

.no-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
