<template>
  <div class="playback-control">
    <!-- 播放控制按钮 -->
    <div class="control-buttons">
      <el-button-group>
        <el-button
          :type="isPlaying ? 'warning' : 'primary'"
          :icon="isPlaying ? 'VideoPause' : 'VideoPlay'"
          @click="togglePlay"
          :disabled="!hasData"
        >
          {{ isPlaying ? '暂停' : '播放' }}
        </el-button>
        
        <el-button
          icon="RefreshLeft"
          @click="resetReplay"
          :disabled="!hasData"
        >
          重置
        </el-button>
      </el-button-group>
    </div>
    
    <!-- 速度控制 -->
    <div class="speed-control">
      <span class="control-label">播放速度:</span>
      <el-select
        v-model="currentSpeed"
        @change="onSpeedChange"
        style="width: 80px"
        size="small"
      >
        <el-option
          v-for="option in speedOptions"
          :key="option.value"
          :label="option.label"
          :value="option.value"
        />
      </el-select>
    </div>
    
    <!-- 时间进度条 -->
    <div class="progress-control">
      <div class="time-info">
        <span class="current-time">{{ formattedCurrentTime }}</span>
        <span class="separator">/</span>
        <span class="total-time">{{ formattedTotalTime }}</span>
      </div>
      
      <div class="progress-slider">
        <el-slider
          v-model="progressValue"
          :min="0"
          :max="100"
          :step="0.1"
          :show-tooltip="false"
          @change="onProgressChange"
          :disabled="!hasData"
        />
      </div>
      
      <div class="progress-percentage">
        {{ progressPercentage }}%
      </div>
    </div>
    
    <!-- 快捷跳转 -->
    <div class="quick-jump">
      <span class="control-label">快捷跳转:</span>
      <el-button-group size="small">
        <el-button @click="jumpToStart" :disabled="!hasData">开始</el-button>
        <el-button @click="jumpBackward" :disabled="!hasData">-10%</el-button>
        <el-button @click="jumpForward" :disabled="!hasData">+10%</el-button>
        <el-button @click="jumpToEnd" :disabled="!hasData">结束</el-button>
      </el-button-group>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useReplayStore } from '../stores/replay'
import { websocketService } from '../services/websocket'

const replayStore = useReplayStore()

// 响应式数据
const progressValue = ref(0)
const currentSpeed = ref(1.0)

// 计算属性
const isPlaying = computed(() => replayStore.isPlaying)
const hasData = computed(() => replayStore.klineData.length > 0)
const speedOptions = computed(() => replayStore.speedOptions)
const progress = computed(() => replayStore.progress)
const currentTime = computed(() => replayStore.currentTime)
const startTime = computed(() => replayStore.startTime)
const endTime = computed(() => replayStore.endTime)
const totalDuration = computed(() => replayStore.totalDuration)

const formattedCurrentTime = computed(() => {
  if (currentTime.value === 0) return '--:--:--'
  return new Date(currentTime.value).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
})

const formattedTotalTime = computed(() => {
  if (endTime.value === 0) return '--:--:--'
  return new Date(endTime.value).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
})

const progressPercentage = computed(() => {
  return (progress.value * 100).toFixed(1)
})

// 方法
const togglePlay = async () => {
  try {
    if (isPlaying.value) {
      await websocketService.pause()
    } else {
      await websocketService.play()
    }
  } catch (error) {
    ElMessage.error('播放控制失败: ' + error.message)
  }
}

const resetReplay = async () => {
  try {
    await websocketService.seek(startTime.value)
    ElMessage.success('已重置到开始位置')
  } catch (error) {
    ElMessage.error('重置失败: ' + error.message)
  }
}

const onSpeedChange = async (speed) => {
  try {
    await websocketService.setSpeed(speed)
  } catch (error) {
    ElMessage.error('设置速度失败: ' + error.message)
  }
}

const onProgressChange = async (value) => {
  if (!hasData.value) return
  
  try {
    // 计算目标时间戳
    const targetTime = startTime.value + (totalDuration.value * value / 100)
    await websocketService.seek(targetTime)
  } catch (error) {
    ElMessage.error('跳转失败: ' + error.message)
  }
}

const jumpToStart = async () => {
  try {
    await websocketService.seek(startTime.value)
  } catch (error) {
    ElMessage.error('跳转到开始失败: ' + error.message)
  }
}

const jumpToEnd = async () => {
  try {
    await websocketService.seek(endTime.value)
  } catch (error) {
    ElMessage.error('跳转到结束失败: ' + error.message)
  }
}

const jumpBackward = async () => {
  try {
    const targetTime = currentTime.value - (totalDuration.value * 0.1)
    const clampedTime = Math.max(startTime.value, targetTime)
    await websocketService.seek(clampedTime)
  } catch (error) {
    ElMessage.error('向后跳转失败: ' + error.message)
  }
}

const jumpForward = async () => {
  try {
    const targetTime = currentTime.value + (totalDuration.value * 0.1)
    const clampedTime = Math.min(endTime.value, targetTime)
    await websocketService.seek(clampedTime)
  } catch (error) {
    ElMessage.error('向前跳转失败: ' + error.message)
  }
}

// 监听进度变化，更新滑块
watch(progress, (newProgress) => {
  progressValue.value = newProgress * 100
})

// 监听速度变化，更新选择器
watch(() => replayStore.speed, (newSpeed) => {
  currentSpeed.value = newSpeed
})
</script>

<style scoped>
.playback-control {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  flex-wrap: wrap;
}

.control-buttons {
  display: flex;
  align-items: center;
}

.speed-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-control {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16px;
  min-width: 300px;
}

.time-info {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.progress-slider {
  flex: 1;
  margin: 0 16px;
}

.progress-percentage {
  font-size: 14px;
  color: #909399;
  min-width: 50px;
  text-align: right;
}

.quick-jump {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.separator {
  color: #c0c4cc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .playback-control {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .progress-control {
    min-width: auto;
  }
  
  .time-info {
    justify-content: center;
  }
}
</style>
