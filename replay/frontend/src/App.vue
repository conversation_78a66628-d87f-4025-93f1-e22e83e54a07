<template>
  <div id="app">
    <el-container class="app-container">
      <el-header class="app-header">
        <h1>股票行情回放系统</h1>
        <div class="connection-status">
          <el-tag :type="isConnected ? 'success' : 'danger'">
            {{ isConnected ? '已连接' : '未连接' }}
          </el-tag>
        </div>
      </el-header>
      
      <el-main class="app-main">
        <router-view />
      </el-main>
    </el-container>
    
    <!-- 全局错误提示 -->
    <el-dialog
      v-model="showError"
      title="错误"
      width="400px"
      :show-close="true"
    >
      <p>{{ error }}</p>
      <template #footer>
        <el-button @click="clearError">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted } from 'vue'
import { useReplayStore } from './stores/replay'
import { ElMessage } from 'element-plus'

const replayStore = useReplayStore()

// 计算属性
const isConnected = computed(() => replayStore.isConnected)
const error = computed(() => replayStore.error)
const showError = computed({
  get: () => !!replayStore.error,
  set: () => replayStore.clearError()
})

// 方法
const clearError = () => {
  replayStore.clearError()
}

// 生命周期
onMounted(() => {
  // 可以在这里初始化一些全局设置
})

onUnmounted(() => {
  // 清理资源
})
</script>

<style scoped>
.app-container {
  height: 100vh;
  background-color: #f5f5f5;
}

.app-header {
  background-color: #409eff;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.app-main {
  padding: 20px;
  height: calc(100vh - 60px);
  overflow: hidden;
}

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}
</style>
