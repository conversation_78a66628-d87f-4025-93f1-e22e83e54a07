import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useReplayStore = defineStore('replay', () => {
  // 回放状态
  const isPlaying = ref(false)
  const currentTime = ref(0)
  const speed = ref(1.0)
  const totalDuration = ref(0)
  const symbol = ref('')
  const period = ref('1m')
  
  // 数据状态
  const klineData = ref([])
  const isLoading = ref(false)
  const error = ref('')
  
  // WebSocket连接状态
  const isConnected = ref(false)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = ref(5)
  
  // 计算属性
  const progress = computed(() => {
    if (totalDuration.value === 0) return 0
    return (currentTime.value - startTime.value) / totalDuration.value
  })
  
  const startTime = computed(() => {
    if (klineData.value.length === 0) return 0
    return klineData.value[0].time
  })
  
  const endTime = computed(() => {
    if (klineData.value.length === 0) return 0
    return klineData.value[klineData.value.length - 1].time
  })
  
  const formattedCurrentTime = computed(() => {
    if (currentTime.value === 0) return ''
    return new Date(currentTime.value).toLocaleString('zh-CN')
  })
  
  const speedOptions = computed(() => [
    { label: '0.1x', value: 0.1 },
    { label: '0.5x', value: 0.5 },
    { label: '1x', value: 1.0 },
    { label: '2x', value: 2.0 },
    { label: '5x', value: 5.0 },
    { label: '10x', value: 10.0 },
    { label: '50x', value: 50.0 },
    { label: '100x', value: 100.0 }
  ])
  
  // 状态更新方法
  const updateReplayStatus = (status) => {
    isPlaying.value = status.is_playing
    currentTime.value = status.current_time
    speed.value = status.speed
    totalDuration.value = status.total_duration
    if (status.symbol) symbol.value = status.symbol
    if (status.period) period.value = status.period
  }
  
  const addKlineData = (data) => {
    klineData.value.push(data)
  }
  
  const updateKlineData = (data) => {
    // 查找是否存在相同时间的数据点
    const existingIndex = klineData.value.findIndex(item => item.time === data.time)
    
    if (existingIndex !== -1) {
      // 更新现有数据点
      klineData.value[existingIndex] = data
    } else {
      // 添加新数据点
      klineData.value.push(data)
      // 保持数据按时间排序
      klineData.value.sort((a, b) => a.time - b.time)
    }
  }
  
  const clearKlineData = () => {
    klineData.value = []
  }
  
  const setLoading = (loading) => {
    isLoading.value = loading
  }
  
  const setError = (errorMsg) => {
    error.value = errorMsg
  }
  
  const clearError = () => {
    error.value = ''
  }
  
  const setConnected = (connected) => {
    isConnected.value = connected
    if (connected) {
      reconnectAttempts.value = 0
    }
  }
  
  const incrementReconnectAttempts = () => {
    reconnectAttempts.value++
  }
  
  const resetReconnectAttempts = () => {
    reconnectAttempts.value = 0
  }
  
  const setSymbol = (newSymbol) => {
    symbol.value = newSymbol
  }
  
  const setPeriod = (newPeriod) => {
    period.value = newPeriod
  }
  
  return {
    // 状态
    isPlaying,
    currentTime,
    speed,
    totalDuration,
    symbol,
    period,
    klineData,
    isLoading,
    error,
    isConnected,
    reconnectAttempts,
    maxReconnectAttempts,
    
    // 计算属性
    progress,
    startTime,
    endTime,
    formattedCurrentTime,
    speedOptions,
    
    // 方法
    updateReplayStatus,
    addKlineData,
    updateKlineData,
    clearKlineData,
    setLoading,
    setError,
    clearError,
    setConnected,
    incrementReconnectAttempts,
    resetReconnectAttempts,
    setSymbol,
    setPeriod
  }
})
