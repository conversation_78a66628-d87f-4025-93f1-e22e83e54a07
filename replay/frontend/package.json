{"name": "stock-replay-frontend", "version": "1.0.0", "description": "股票行情回放系统前端", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.0", "pinia": "^2.1.0", "axios": "^1.6.0", "lightweight-charts": "^4.1.0", "element-plus": "^2.4.0", "@element-plus/icons-vue": "^2.3.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "vite": "^5.0.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.17.0", "@vue/eslint-config-prettier": "^9.0.0", "prettier": "^3.0.0"}}