# 股票行情回放系统使用指南

## 快速启动

### 1. 安装后端依赖

```bash
cd replay/backend
pip install -r requirements.txt
```

### 2. 启动后端服务

```bash
cd replay
python start_backend.py
```

后端服务将在 http://localhost:8000 启动

### 3. 安装前端依赖

```bash
cd replay/frontend
npm install
```

### 4. 启动前端服务

```bash
npm run dev
```

前端服务将在 http://localhost:3000 启动

## 功能说明

### 后端功能

✅ **数据加载**: 自动读取 `examples/` 目录下的 `.feather` 文件
✅ **多周期聚合**: 支持 1m, 5m, 15m, 30m, 1h, 4h, 1d 时间周期
✅ **WebSocket推送**: 实时数据推送和回放控制
✅ **REST API**: 提供数据查询和回放控制接口

### 前端功能

✅ **K线图表**: 基于 lightweight-charts 的交互式图表
✅ **播放控制**: 播放/暂停/速度调节/时间跳转
✅ **实时更新**: WebSocket实时数据接收和图表更新
✅ **响应式界面**: 基于 Element Plus 的现代化界面

## API接口测试

后端启动后，可以访问以下地址进行测试：

- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **获取品种**: http://localhost:8000/api/data/symbols
- **获取周期**: http://localhost:8000/api/data/get_periods

## 数据格式要求

Feather文件应包含以下列（按顺序）：
1. `timestamp` - 时间戳（秒或毫秒）
2. `open` - 开盘价
3. `high` - 最高价  
4. `low` - 最低价
5. `close` - 收盘价
6. `volume` - 成交量

## 使用流程

1. **启动服务**: 先启动后端，再启动前端
2. **选择品种**: 在前端界面选择要回放的交易品种
3. **选择周期**: 选择K线时间周期
4. **加载数据**: 点击"加载数据"按钮
5. **开始回放**: 使用播放控制面板控制回放

## 故障排除

### 后端启动失败
- 检查Python环境和依赖安装
- 确认端口8000未被占用
- 查看控制台错误信息

### 前端启动失败  
- 检查Node.js环境
- 确认依赖安装完成
- 检查端口3000未被占用

### 数据加载失败
- 确认 `examples/` 目录下有 `.feather` 文件
- 检查文件格式是否正确
- 查看后端日志错误信息

## 开发说明

项目已经完成了基础功能实现，包括：
- 完整的后端API服务
- 前端Vue3应用框架
- WebSocket实时通信
- K线图表显示
- 播放控制功能

可以根据需要进一步扩展功能，如添加技术指标、订单簿显示等。
