# 股票行情回放系统

基于Feather文件的股票行情回放系统，功能类似TradingView，支持tick级别数据的实时回放、K线图展示和交互操作。

## 系统架构

- **后端**: FastAPI + WebSocket + Pandas
- **前端**: Vue3 + Element Plus + Lightweight Charts
- **数据**: Feather文件格式

## 功能特性

- ✅ Feather文件数据加载
- ✅ 多时间周期K线聚合（1m, 5m, 15m, 30m, 1h, 4h, 1d）
- ✅ 实时回放控制（播放/暂停/倍速/跳转）
- ✅ 交互式K线图表（缩放/平移/十字线）
- ✅ WebSocket实时数据推送
- ✅ 响应式前端界面

## 快速开始

### 开发环境

#### 后端启动

```bash
cd replay/backend
pip install -r requirements.txt
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

#### 前端启动

```bash
cd replay/frontend
npm install
npm run dev
```

### 生产环境

使用Docker Compose一键部署：

```bash
cd replay
docker-compose up -d
```

访问地址：
- 前端界面: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

## 项目结构

```
replay/
├── backend/                 # 后端FastAPI应用
│   ├── main.py             # 主应用入口
│   ├── config.py           # 配置文件
│   ├── models/             # 数据模型
│   │   ├── data_models.py  # 数据模型
│   │   └── websocket_models.py # WebSocket消息模型
│   ├── routers/            # API路由
│   │   ├── data_router.py  # 数据相关API
│   │   └── replay_router.py # 回放相关API
│   ├── services/           # 业务服务
│   │   ├── data_service.py # 数据服务
│   │   └── replay_engine.py # 回放引擎
│   ├── requirements.txt    # Python依赖
│   └── Dockerfile         # Docker配置
├── frontend/               # 前端Vue3应用
│   ├── src/
│   │   ├── components/     # Vue组件
│   │   │   ├── KLineChart.vue # K线图表组件
│   │   │   └── PlaybackControl.vue # 播放控制组件
│   │   ├── views/          # 页面视图
│   │   │   └── ReplayView.vue # 主回放页面
│   │   ├── stores/         # 状态管理
│   │   │   └── replay.js   # 回放状态store
│   │   ├── services/       # 服务层
│   │   │   ├── api.js      # HTTP API服务
│   │   │   └── websocket.js # WebSocket服务
│   │   ├── router/         # 路由配置
│   │   ├── App.vue         # 根组件
│   │   └── main.js         # 应用入口
│   ├── package.json        # 前端依赖
│   ├── vite.config.js      # Vite配置
│   ├── nginx.conf          # Nginx配置
│   └── Dockerfile         # Docker配置
├── docker-compose.yml      # Docker Compose配置
└── README.md              # 项目文档
```

## API接口

### HTTP API

- `GET /api/data/symbols` - 获取可用交易品种
- `GET /api/data/get_periods` - 获取支持的时间周期
- `GET /api/data/load_data` - 加载历史数据
- `GET /api/data/data_range/{symbol}` - 获取数据时间范围
- `GET /api/replay/status` - 获取回放状态
- `POST /api/replay/control` - 控制回放
- `POST /api/replay/reset` - 重置回放

### WebSocket API

- `ws://localhost:8000/ws/replay` - 回放数据推送

消息格式：
```json
{
  "action": "play|pause|seek|speed|load",
  "payload": {
    "symbol": "EURUSD",
    "period": "1m",
    "timestamp": 1735779600000,
    "speed": 2.0
  }
}
```

## 数据格式

Feather文件应包含以下字段：
- `timestamp`: 时间戳（秒或毫秒）
- `open`: 开盘价
- `high`: 最高价
- `low`: 最低价
- `close`: 收盘价
- `volume`: 成交量

## 配置说明

### 后端配置 (config.py)

- `HOST`: 服务器地址
- `PORT`: 服务器端口
- `DATA_PATH`: 数据文件路径
- `SUPPORTED_PERIODS`: 支持的时间周期
- `MAX_REPLAY_SPEED`: 最大回放速度

### 前端配置 (vite.config.js)

- 开发服务器端口: 3000
- API代理配置
- WebSocket代理配置

## 开发指南

### 添加新的时间周期

1. 在 `backend/config.py` 中添加到 `SUPPORTED_PERIODS`
2. 在 `backend/services/data_service.py` 中更新 `period_map`
3. 前端会自动获取支持的周期列表

### 扩展数据源

1. 在 `DataService` 中添加新的数据读取方法
2. 更新数据模型以支持新字段
3. 修改前端图表组件以显示新数据

## 故障排除

### 常见问题

1. **WebSocket连接失败**
   - 检查后端服务是否正常运行
   - 确认防火墙设置
   - 查看浏览器控制台错误信息

2. **数据加载失败**
   - 确认Feather文件路径正确
   - 检查文件格式是否符合要求
   - 查看后端日志错误信息

3. **图表显示异常**
   - 检查数据格式是否正确
   - 确认时间戳格式（秒/毫秒）
   - 查看浏览器控制台错误

### 日志查看

```bash
# 查看后端日志
docker-compose logs backend

# 查看前端日志
docker-compose logs frontend

# 实时查看日志
docker-compose logs -f
```

## 许可证

MIT License
