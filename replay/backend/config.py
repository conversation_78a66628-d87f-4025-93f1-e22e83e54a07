"""
配置文件
"""
import os
from pathlib import Path
from typing import List


class Settings:
    """应用配置"""
    
    # 服务器配置
    HOST: str = os.getenv("HOST", "127.0.0.1")
    PORT: int = int(os.getenv("PORT", "8000"))
    DEBUG: bool = os.getenv("DEBUG", "True").lower() == "true"
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:8080",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:8080",
    ]
    
    # 数据路径配置
    DATA_PATH: Path = Path(os.getenv("DATA_PATH", "../../examples"))
    
    # 回放配置
    DEFAULT_REPLAY_SPEED: float = 1.0  # 默认回放速度
    MAX_REPLAY_SPEED: float = 100.0    # 最大回放速度
    MIN_REPLAY_SPEED: float = 0.1      # 最小回放速度
    
    # WebSocket配置
    WEBSOCKET_HEARTBEAT_INTERVAL: int = 30  # 心跳间隔（秒）
    MAX_CLIENTS: int = 100  # 最大客户端连接数
    
    # 数据配置
    SUPPORTED_PERIODS: List[str] = ["1m", "5m", "15m", "30m", "1h", "4h", "1d"]
    DEFAULT_PERIOD: str = "1m"
    MAX_BARS_PER_REQUEST: int = 10000  # 单次请求最大K线数量
    
    # 缓存配置
    ENABLE_CACHE: bool = True
    CACHE_TTL: int = 300  # 缓存过期时间（秒）


# 创建全局配置实例
settings = Settings()
