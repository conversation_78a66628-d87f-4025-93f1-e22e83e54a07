"""
回放相关API路由
"""
import logging
from typing import Optional
from fastapi import APIRouter, HTTPException, Depends, Query

from models.data_models import ReplayStatus, ErrorResponse
from models.websocket_models import ReplayControl
from services.replay_engine import ReplayEngine
from config import settings

logger = logging.getLogger(__name__)

router = APIRouter()

# 全局变量
_replay_engine: Optional[ReplayEngine] = None

def set_replay_engine(engine: ReplayEngine):
    """设置回放引擎实例"""
    global _replay_engine
    _replay_engine = engine

# 依赖注入
def get_replay_engine() -> ReplayEngine:
    """获取回放引擎实例"""
    if _replay_engine is None:
        raise HTTPException(status_code=500, detail="回放引擎未初始化")
    return _replay_engine


@router.get("/status", response_model=ReplayStatus)
async def get_replay_status(
    replay_engine: ReplayEngine = Depends(get_replay_engine)
):
    """
    获取当前回放状态
    
    返回回放引擎的当前状态信息
    """
    try:
        return ReplayStatus(
            is_playing=replay_engine.is_playing,
            current_time=replay_engine.current_time,
            speed=replay_engine.speed,
            total_duration=replay_engine.end_time - replay_engine.start_time if replay_engine.data_list else 0,
            progress=replay_engine.current_index / len(replay_engine.data_list) if replay_engine.data_list else 0,
            symbol=replay_engine.symbol,
            period=replay_engine.period
        )
    except Exception as e:
        logger.error(f"获取回放状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.post("/control")
async def control_replay(
    control: ReplayControl,
    replay_engine: ReplayEngine = Depends(get_replay_engine)
):
    """
    控制回放
    
    - **action**: 控制动作（play, pause, seek, speed, load）
    - **value**: 控制参数值
    - **symbol**: 交易品种（load动作时需要）
    - **period**: 时间周期（load动作时需要）
    """
    try:
        # 构造消息
        message = {
            "action": control.action,
            "payload": {}
        }
        
        if control.value is not None:
            if control.action == "seek":
                message["payload"]["timestamp"] = control.value
            elif control.action == "speed":
                message["payload"]["speed"] = control.value
        
        if control.symbol:
            message["payload"]["symbol"] = control.symbol
            
        if control.period:
            message["payload"]["period"] = control.period
        
        # 处理控制命令（使用第一个客户端ID，如果没有客户端则创建虚拟处理）
        if replay_engine.clients:
            client_id = next(iter(replay_engine.clients.keys()))
            await replay_engine.handle_client_message(client_id, message)
        else:
            # 没有WebSocket客户端时的直接处理
            if control.action == "load":
                await replay_engine._handle_load(control.symbol, control.period)
            elif control.action == "play":
                await replay_engine._handle_play({})
            elif control.action == "pause":
                await replay_engine._handle_pause()
            elif control.action == "seek":
                await replay_engine._handle_seek(control.value)
            elif control.action == "speed":
                await replay_engine._handle_speed(control.value)
        
        return {"status": "success", "message": f"执行 {control.action} 命令成功"}
        
    except Exception as e:
        logger.error(f"控制回放失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.get("/config")
async def get_replay_config():
    """
    获取回放配置信息
    
    返回回放系统的配置参数
    """
    return {
        "supported_periods": settings.SUPPORTED_PERIODS,
        "default_period": settings.DEFAULT_PERIOD,
        "speed_range": {
            "min": settings.MIN_REPLAY_SPEED,
            "max": settings.MAX_REPLAY_SPEED,
            "default": settings.DEFAULT_REPLAY_SPEED
        },
        "max_bars_per_request": settings.MAX_BARS_PER_REQUEST,
        "max_clients": settings.MAX_CLIENTS
    }


@router.post("/reset")
async def reset_replay(
    replay_engine: ReplayEngine = Depends(get_replay_engine)
):
    """
    重置回放状态
    
    停止当前回放并重置所有状态
    """
    try:
        # 停止播放
        await replay_engine._handle_pause()
        
        # 重置状态
        replay_engine.current_index = 0
        replay_engine.current_time = replay_engine.start_time if replay_engine.data_list else 0
        
        # 广播状态更新
        await replay_engine._broadcast_status_update()
        
        return {"status": "success", "message": "回放状态已重置"}
        
    except Exception as e:
        logger.error(f"重置回放失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
