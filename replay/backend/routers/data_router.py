"""
数据相关API路由
"""
import logging
from typing import Optional, List
from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.responses import JSONResponse

from models.data_models import (
    HistoryDataRequest, HistoryDataResponse, SupportedPeriodsResponse,
    KLineData, ErrorResponse
)
from services.data_service import DataService
from config import settings

logger = logging.getLogger(__name__)

router = APIRouter()

# 全局变量
_data_service: Optional[DataService] = None

def set_data_service(service: DataService):
    """设置数据服务实例"""
    global _data_service
    _data_service = service

# 依赖注入
def get_data_service() -> DataService:
    """获取数据服务实例"""
    if _data_service is None:
        raise HTTPException(status_code=500, detail="数据服务未初始化")
    return _data_service


@router.get("/load_data", response_model=HistoryDataResponse)
async def load_historical_data(
    symbol: str = Query(..., description="交易品种"),
    start_time: Optional[int] = Query(None, description="开始时间戳（毫秒）"),
    end_time: Optional[int] = Query(None, description="结束时间戳（毫秒）"),
    period: str = Query("1m", description="时间周期"),
    limit: int = Query(1000, description="数据条数限制", le=settings.MAX_BARS_PER_REQUEST),
    data_service: DataService = Depends(get_data_service)
):
    """
    按时间段加载历史数据
    
    - **symbol**: 交易品种（如EURUSD、XAUUSD等）
    - **start_time**: 开始时间戳（毫秒），可选
    - **end_time**: 结束时间戳（毫秒），可选  
    - **period**: 时间周期（1m, 5m, 15m, 30m, 1h, 4h, 1d）
    - **limit**: 返回数据条数限制
    """
    try:
        # 验证时间周期
        if period not in settings.SUPPORTED_PERIODS:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的时间周期: {period}，支持的周期: {settings.SUPPORTED_PERIODS}"
            )
        
        # 获取K线数据
        kline_data = data_service.get_kline_data(
            symbol=symbol,
            period=period,
            start_time=start_time,
            end_time=end_time,
            limit=limit
        )
        
        if not kline_data:
            raise HTTPException(
                status_code=404,
                detail=f"未找到品种 {symbol} 的数据"
            )
        
        # 获取数据范围信息
        data_range = data_service.get_data_range(symbol)
        total_count = data_range.get("total_count", 0) if data_range else 0
        
        return HistoryDataResponse(
            symbol=symbol,
            period=period,
            data=kline_data,
            total_count=total_count,
            has_more=len(kline_data) >= limit
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"加载历史数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.get("/get_periods", response_model=SupportedPeriodsResponse)
async def get_supported_periods():
    """
    获取支持的时间周期
    
    返回系统支持的所有时间周期列表
    """
    return SupportedPeriodsResponse(
        periods=settings.SUPPORTED_PERIODS,
        default_period=settings.DEFAULT_PERIOD
    )


@router.get("/symbols")
async def get_available_symbols(
    data_service: DataService = Depends(get_data_service)
):
    """
    获取可用的交易品种
    
    返回系统中可用的所有交易品种列表
    """
    try:
        symbols = data_service.get_available_symbols()
        return {
            "symbols": symbols,
            "count": len(symbols)
        }
    except Exception as e:
        logger.error(f"获取交易品种失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.get("/data_range/{symbol}")
async def get_data_range(
    symbol: str,
    data_service: DataService = Depends(get_data_service)
):
    """
    获取指定品种的数据时间范围
    
    - **symbol**: 交易品种
    """
    try:
        data_range = data_service.get_data_range(symbol)
        
        if not data_range:
            raise HTTPException(
                status_code=404,
                detail=f"未找到品种 {symbol} 的数据"
            )
        
        return data_range
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取数据范围失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.get("/health")
async def health_check():
    """数据服务健康检查"""
    try:
        data_service = get_data_service()
        symbols = data_service.get_available_symbols()
        
        return {
            "status": "healthy",
            "available_symbols": len(symbols),
            "supported_periods": len(settings.SUPPORTED_PERIODS)
        }
    except Exception as e:
        return JSONResponse(
            status_code=503,
            content={"status": "unhealthy", "error": str(e)}
        )
