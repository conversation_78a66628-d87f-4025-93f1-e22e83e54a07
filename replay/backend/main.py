"""
股票行情回放系统 - FastAPI 主应用
"""
import asyncio
import logging
from contextlib import asynccontextmanager
from pathlib import Path
from typing import Dict, Any

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn

from services.replay_engine import ReplayEngine
from services.data_service import DataService
from config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 全局服务实例
replay_engine: ReplayEngine = None
data_service: DataService = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global replay_engine, data_service
    
    # 启动时初始化服务
    logger.info("初始化回放系统服务...")
    data_service = DataService(settings.DATA_PATH)
    replay_engine = ReplayEngine(data_service)

    # 设置全局服务实例
    from routers.data_router import set_data_service
    from routers.replay_router import set_replay_engine
    set_data_service(data_service)
    set_replay_engine(replay_engine)
    
    yield
    
    # 关闭时清理资源
    logger.info("清理回放系统资源...")
    if replay_engine:
        await replay_engine.cleanup()


# 创建FastAPI应用
app = FastAPI(
    title="股票行情回放系统",
    description="基于Feather文件的股票行情回放系统API",
    version="1.0.0",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
from routers import data_router, replay_router
app.include_router(data_router.router, prefix="/api/data", tags=["数据"])
app.include_router(replay_router.router, prefix="/api/replay", tags=["回放"])

# 静态文件服务（用于前端）
if Path("../frontend/dist").exists():
    app.mount("/", StaticFiles(directory="../frontend/dist", html=True), name="static")


@app.get("/")
async def root():
    """根路径"""
    return {"message": "股票行情回放系统API", "version": "1.0.0"}


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "data_service": data_service is not None,
        "replay_engine": replay_engine is not None
    }


@app.websocket("/ws/replay")
async def websocket_replay_endpoint(websocket: WebSocket):
    """WebSocket回放端点"""
    await websocket.accept()
    client_id = id(websocket)
    
    try:
        # 将客户端添加到回放引擎
        await replay_engine.add_client(client_id, websocket)
        logger.info(f"客户端 {client_id} 已连接")
        
        # 监听客户端消息
        while True:
            try:
                message = await websocket.receive_json()
                await replay_engine.handle_client_message(client_id, message)
            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"处理客户端消息时出错: {e}")
                await websocket.send_json({
                    "type": "error",
                    "message": str(e)
                })
                
    except WebSocketDisconnect:
        logger.info(f"客户端 {client_id} 已断开连接")
    except Exception as e:
        logger.error(f"WebSocket连接出错: {e}")
    finally:
        # 清理客户端连接
        await replay_engine.remove_client(client_id)


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
