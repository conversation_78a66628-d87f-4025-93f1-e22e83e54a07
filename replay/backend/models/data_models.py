"""
数据模型定义
"""
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field


class TickData(BaseModel):
    """Tick数据模型"""
    timestamp: int = Field(..., description="时间戳（毫秒）")
    open: float = Field(..., description="开盘价")
    high: float = Field(..., description="最高价")
    low: float = Field(..., description="最低价")
    close: float = Field(..., description="收盘价")
    volume: float = Field(..., description="成交量")
    symbol: str = Field(..., description="交易品种")
    
    class Config:
        json_encoders = {
            datetime: lambda v: int(v.timestamp() * 1000)
        }


class KLineData(BaseModel):
    """K线数据模型"""
    time: int = Field(..., description="时间戳（毫秒）")
    open: float = Field(..., description="开盘价")
    high: float = Field(..., description="最高价")
    low: float = Field(..., description="最低价")
    close: float = Field(..., description="收盘价")
    volume: float = Field(..., description="成交量")


class HistoryDataRequest(BaseModel):
    """历史数据请求模型"""
    symbol: str = Field(..., description="交易品种")
    start_time: Optional[int] = Field(None, description="开始时间戳（毫秒）")
    end_time: Optional[int] = Field(None, description="结束时间戳（毫秒）")
    period: str = Field("1m", description="时间周期")
    limit: int = Field(1000, description="数据条数限制", le=10000)


class HistoryDataResponse(BaseModel):
    """历史数据响应模型"""
    symbol: str = Field(..., description="交易品种")
    period: str = Field(..., description="时间周期")
    data: List[KLineData] = Field(..., description="K线数据")
    total_count: int = Field(..., description="总数据条数")
    has_more: bool = Field(..., description="是否还有更多数据")


class SupportedPeriodsResponse(BaseModel):
    """支持的时间周期响应模型"""
    periods: List[str] = Field(..., description="支持的时间周期列表")
    default_period: str = Field(..., description="默认时间周期")


class ReplayStatus(BaseModel):
    """回放状态模型"""
    is_playing: bool = Field(..., description="是否正在播放")
    current_time: int = Field(..., description="当前时间戳（毫秒）")
    speed: float = Field(..., description="播放速度")
    total_duration: int = Field(..., description="总时长（毫秒）")
    progress: float = Field(..., description="播放进度（0-1）")
    symbol: Optional[str] = Field(None, description="当前播放的交易品种")
    period: Optional[str] = Field(None, description="当前时间周期")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    detail: Optional[str] = Field(None, description="错误详情")
