"""
WebSocket消息模型
"""
from typing import Optional, Any, Dict, Literal
from pydantic import BaseModel, Field


class ReplayControl(BaseModel):
    """回放控制消息模型"""
    action: Literal["play", "pause", "seek", "speed", "load"] = Field(..., description="控制动作")
    value: Optional[Any] = Field(None, description="控制参数值")
    symbol: Optional[str] = Field(None, description="交易品种")
    period: Optional[str] = Field(None, description="时间周期")


class WebSocketMessage(BaseModel):
    """WebSocket消息基础模型"""
    type: str = Field(..., description="消息类型")
    data: Optional[Dict[str, Any]] = Field(None, description="消息数据")
    timestamp: Optional[int] = Field(None, description="消息时间戳")


class KLineUpdateMessage(WebSocketMessage):
    """K线更新消息"""
    type: Literal["kline_update"] = "kline_update"
    data: Dict[str, Any] = Field(..., description="K线数据")


class StatusUpdateMessage(WebSocketMessage):
    """状态更新消息"""
    type: Literal["status_update"] = "status_update"
    data: Dict[str, Any] = Field(..., description="状态数据")


class ErrorMessage(WebSocketMessage):
    """错误消息"""
    type: Literal["error"] = "error"
    data: Dict[str, str] = Field(..., description="错误信息")


class HeartbeatMessage(WebSocketMessage):
    """心跳消息"""
    type: Literal["heartbeat"] = "heartbeat"
    data: Optional[Dict[str, Any]] = None


class ClientMessage(BaseModel):
    """客户端发送的消息模型"""
    action: str = Field(..., description="动作类型")
    payload: Optional[Dict[str, Any]] = Field(None, description="消息载荷")
