"""
数据服务 - 负责Feather文件读取和数据处理
"""
import logging
import pandas as pd
from pathlib import Path
from typing import List, Optional, Dict, Any, Iterator
from datetime import datetime, timezone
import numpy as np

from models.data_models import TickData, KLineData
from config import settings

logger = logging.getLogger(__name__)


class DataService:
    """数据服务类"""
    
    def __init__(self, data_path: Path):
        self.data_path = Path(data_path)
        self._cache: Dict[str, pd.DataFrame] = {}
        
    def get_available_symbols(self) -> List[str]:
        """获取可用的交易品种"""
        symbols = []
        for file_path in self.data_path.glob("*.feather"):
            # 从文件名提取品种名称
            symbol = file_path.stem.split('_')[0] if '_' in file_path.stem else file_path.stem
            symbols.append(symbol)
        return list(set(symbols))
    
    def load_feather_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """加载Feather文件数据"""
        cache_key = f"{symbol}_raw"
        
        # 检查缓存
        if settings.ENABLE_CACHE and cache_key in self._cache:
            logger.info(f"从缓存加载数据: {symbol}")
            return self._cache[cache_key]
        
        # 查找对应的feather文件
        feather_files = list(self.data_path.glob(f"{symbol}*.feather"))
        if not feather_files:
            logger.error(f"未找到品种 {symbol} 的数据文件")
            return None
            
        file_path = feather_files[0]  # 使用第一个匹配的文件
        
        try:
            logger.info(f"加载Feather文件: {file_path}")
            df = pd.read_feather(file_path)
            
            # 标准化列名
            if len(df.columns) >= 6:
                df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])
            
            # 确保时间戳为毫秒
            if df['timestamp'].max() < 1e12:  # 如果是秒级时间戳
                df['timestamp'] = df['timestamp'] * 1000
                
            # 按时间戳排序
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            # 缓存数据
            if settings.ENABLE_CACHE:
                self._cache[cache_key] = df
                
            logger.info(f"成功加载 {len(df)} 条数据记录")
            return df
            
        except Exception as e:
            logger.error(f"加载Feather文件失败: {e}")
            return None
    
    def get_tick_data(self, symbol: str, start_time: Optional[int] = None, 
                     end_time: Optional[int] = None, limit: int = 1000) -> List[TickData]:
        """获取Tick数据"""
        df = self.load_feather_data(symbol)
        if df is None:
            return []
        
        # 时间过滤
        if start_time:
            df = df[df['timestamp'] >= start_time]
        if end_time:
            df = df[df['timestamp'] <= end_time]
            
        # 限制数量
        df = df.head(limit)
        
        # 转换为TickData模型
        tick_data = []
        for _, row in df.iterrows():
            tick_data.append(TickData(
                timestamp=int(row['timestamp']),
                open=float(row['open']),
                high=float(row['high']),
                low=float(row['low']),
                close=float(row['close']),
                volume=float(row['volume']),
                symbol=symbol
            ))
        
        return tick_data
    
    def aggregate_to_kline(self, df: pd.DataFrame, period: str) -> pd.DataFrame:
        """将tick数据聚合为K线数据"""
        if df.empty:
            return df
            
        # 转换时间戳为datetime
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)
        df.set_index('datetime', inplace=True)
        
        # 定义聚合周期映射
        period_map = {
            '1m': '1min',
            '5m': '5min',
            '15m': '15min',
            '30m': '30min',
            '1h': '1h',
            '4h': '4h',
            '1d': '1D'
        }
        
        if period not in period_map:
            raise ValueError(f"不支持的时间周期: {period}")
            
        freq = period_map[period]
        
        # 聚合数据
        agg_data = df.resample(freq).agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }).dropna()
        
        # 重置索引并转换时间戳
        agg_data.reset_index(inplace=True)
        agg_data['time'] = (agg_data['datetime'].astype('int64') // 1000000).astype('int64')
        
        return agg_data[['time', 'open', 'high', 'low', 'close', 'volume']]
    
    def get_kline_data(self, symbol: str, period: str = "1m", 
                      start_time: Optional[int] = None, end_time: Optional[int] = None,
                      limit: int = 1000) -> List[KLineData]:
        """获取K线数据"""
        df = self.load_feather_data(symbol)
        if df is None:
            return []
        
        # 时间过滤
        if start_time:
            df = df[df['timestamp'] >= start_time]
        if end_time:
            df = df[df['timestamp'] <= end_time]
        
        # 聚合为K线
        kline_df = self.aggregate_to_kline(df, period)
        
        # 限制数量
        kline_df = kline_df.tail(limit)
        
        # 转换为KLineData模型
        kline_data = []
        for _, row in kline_df.iterrows():
            kline_data.append(KLineData(
                time=int(row['time']),
                open=float(row['open']),
                high=float(row['high']),
                low=float(row['low']),
                close=float(row['close']),
                volume=float(row['volume'])
            ))
        
        return kline_data
    
    def get_data_iterator(self, symbol: str, period: str = "1m") -> Iterator[KLineData]:
        """获取数据迭代器，用于回放"""
        df = self.load_feather_data(symbol)
        if df is None:
            return
            
        # 聚合为K线
        kline_df = self.aggregate_to_kline(df, period)
        
        # 逐条返回数据
        for _, row in kline_df.iterrows():
            yield KLineData(
                time=int(row['time']),
                open=float(row['open']),
                high=float(row['high']),
                low=float(row['low']),
                close=float(row['close']),
                volume=float(row['volume'])
            )
    
    def get_data_range(self, symbol: str) -> Optional[Dict[str, int]]:
        """获取数据的时间范围"""
        df = self.load_feather_data(symbol)
        if df is None or df.empty:
            return None
            
        return {
            "start_time": int(df['timestamp'].min()),
            "end_time": int(df['timestamp'].max()),
            "total_count": len(df)
        }
