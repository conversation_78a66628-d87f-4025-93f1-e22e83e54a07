"""
回放引擎 - 负责WebSocket数据推送和回放控制
"""
import asyncio
import logging
import time
from typing import Dict, Optional, List, Iterator, Any
from fastapi import WebSocket

from models.data_models import KLineData, ReplayStatus
from models.websocket_models import (
    ReplayControl, KLineUpdateMessage, StatusUpdateMessage,
    ErrorMessage, HeartbeatMessage
)
from services.data_service import DataService
from config import settings

logger = logging.getLogger(__name__)


class ReplayClient:
    """回放客户端"""
    
    def __init__(self, client_id: int, websocket: WebSocket):
        self.client_id = client_id
        self.websocket = websocket
        self.last_heartbeat = time.time()


class ReplayEngine:
    """回放引擎"""
    
    def __init__(self, data_service: DataService):
        self.data_service = data_service
        self.clients: Dict[int, ReplayClient] = {}
        
        # 回放状态
        self.is_playing = False
        self.current_time = 0
        self.speed = settings.DEFAULT_REPLAY_SPEED
        self.symbol: Optional[str] = None
        self.period: Optional[str] = None
        
        # 数据相关
        self.data_iterator: Optional[Iterator[KLineData]] = None
        self.data_list: List[KLineData] = []
        self.current_index = 0
        self.start_time = 0
        self.end_time = 0
        
        # 任务管理
        self.replay_task: Optional[asyncio.Task] = None
        self.heartbeat_task: Optional[asyncio.Task] = None
        
        # 启动心跳任务
        self._start_heartbeat()
    
    async def add_client(self, client_id: int, websocket: WebSocket):
        """添加客户端"""
        if len(self.clients) >= settings.MAX_CLIENTS:
            await websocket.close(code=1008, reason="服务器客户端连接数已满")
            return
            
        self.clients[client_id] = ReplayClient(client_id, websocket)
        
        # 发送当前状态
        await self._send_status_update(client_id)
        
    async def remove_client(self, client_id: int):
        """移除客户端"""
        if client_id in self.clients:
            del self.clients[client_id]
            logger.info(f"客户端 {client_id} 已移除")
    
    async def handle_client_message(self, client_id: int, message: Dict[str, Any]):
        """处理客户端消息"""
        try:
            action = message.get("action")
            payload = message.get("payload", {})
            
            if action == "play":
                await self._handle_play(payload)
            elif action == "pause":
                await self._handle_pause()
            elif action == "seek":
                await self._handle_seek(payload.get("timestamp"))
            elif action == "speed":
                await self._handle_speed(payload.get("speed"))
            elif action == "load":
                await self._handle_load(payload.get("symbol"), payload.get("period"))
            elif action == "heartbeat":
                await self._handle_heartbeat(client_id)
            else:
                await self._send_error(client_id, f"未知的动作: {action}")
                
        except Exception as e:
            logger.error(f"处理客户端消息时出错: {e}")
            await self._send_error(client_id, str(e))
    
    async def _handle_play(self, payload: Dict[str, Any]):
        """处理播放命令"""
        if not self.data_list:
            await self._broadcast_error("没有加载数据，无法播放")
            return
            
        if not self.is_playing:
            self.is_playing = True
            await self._broadcast_status_update()
            
            # 启动回放任务
            if self.replay_task is None or self.replay_task.done():
                self.replay_task = asyncio.create_task(self._replay_loop())
    
    async def _handle_pause(self):
        """处理暂停命令"""
        self.is_playing = False
        await self._broadcast_status_update()
        
        # 取消回放任务
        if self.replay_task and not self.replay_task.done():
            self.replay_task.cancel()
    
    async def _handle_seek(self, timestamp: Optional[int]):
        """处理跳转命令"""
        if not timestamp or not self.data_list:
            return
            
        # 查找最接近的数据点
        for i, data in enumerate(self.data_list):
            if data.time >= timestamp:
                self.current_index = i
                self.current_time = data.time
                break
        
        await self._broadcast_status_update()
    
    async def _handle_speed(self, speed: Optional[float]):
        """处理速度调整命令"""
        if speed is None:
            return
            
        # 限制速度范围
        self.speed = max(settings.MIN_REPLAY_SPEED, 
                        min(settings.MAX_REPLAY_SPEED, speed))
        
        await self._broadcast_status_update()
    
    async def _handle_load(self, symbol: Optional[str], period: Optional[str]):
        """处理数据加载命令"""
        if not symbol:
            await self._broadcast_error("缺少交易品种参数")
            return
            
        period = period or settings.DEFAULT_PERIOD
        
        try:
            # 停止当前回放
            await self._handle_pause()
            
            # 加载新数据
            self.symbol = symbol
            self.period = period
            self.data_list = self.data_service.get_kline_data(symbol, period)
            
            if not self.data_list:
                await self._broadcast_error(f"无法加载品种 {symbol} 的数据")
                return
            
            # 重置状态
            self.current_index = 0
            self.current_time = self.data_list[0].time if self.data_list else 0
            self.start_time = self.data_list[0].time if self.data_list else 0
            self.end_time = self.data_list[-1].time if self.data_list else 0
            
            await self._broadcast_status_update()
            logger.info(f"成功加载 {symbol} {period} 数据，共 {len(self.data_list)} 条")
            
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            await self._broadcast_error(f"加载数据失败: {str(e)}")
    
    async def _handle_heartbeat(self, client_id: int):
        """处理心跳"""
        if client_id in self.clients:
            self.clients[client_id].last_heartbeat = time.time()
            await self._send_heartbeat(client_id)
    
    async def _replay_loop(self):
        """回放循环"""
        try:
            while self.is_playing and self.current_index < len(self.data_list):
                current_data = self.data_list[self.current_index]
                
                # 发送K线数据
                await self._broadcast_kline_update(current_data)
                
                # 更新当前时间和索引
                self.current_time = current_data.time
                self.current_index += 1
                
                # 发送状态更新
                await self._broadcast_status_update()
                
                # 根据速度计算延迟
                if self.current_index < len(self.data_list):
                    next_data = self.data_list[self.current_index]
                    time_diff = (next_data.time - current_data.time) / 1000.0  # 转换为秒
                    delay = time_diff / self.speed
                    
                    # 最小延迟10ms，最大延迟5秒
                    delay = max(0.01, min(5.0, delay))
                    await asyncio.sleep(delay)
            
            # 播放完成
            if self.current_index >= len(self.data_list):
                self.is_playing = False
                await self._broadcast_status_update()
                logger.info("回放完成")
                
        except asyncio.CancelledError:
            logger.info("回放任务被取消")
        except Exception as e:
            logger.error(f"回放循环出错: {e}")
            self.is_playing = False
            await self._broadcast_error(f"回放出错: {str(e)}")
    
    def _start_heartbeat(self):
        """启动心跳任务"""
        self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
    
    async def _heartbeat_loop(self):
        """心跳循环"""
        while True:
            try:
                await asyncio.sleep(settings.WEBSOCKET_HEARTBEAT_INTERVAL)
                
                # 检查客户端连接状态
                current_time = time.time()
                disconnected_clients = []
                
                for client_id, client in self.clients.items():
                    if current_time - client.last_heartbeat > settings.WEBSOCKET_HEARTBEAT_INTERVAL * 2:
                        disconnected_clients.append(client_id)
                
                # 移除断开连接的客户端
                for client_id in disconnected_clients:
                    await self.remove_client(client_id)
                    
            except Exception as e:
                logger.error(f"心跳检查出错: {e}")
    
    async def _broadcast_kline_update(self, data: KLineData):
        """广播K线更新"""
        message = KLineUpdateMessage(
            data=data.dict(),
            timestamp=int(time.time() * 1000)
        )
        await self._broadcast_message(message.dict())
    
    async def _broadcast_status_update(self):
        """广播状态更新"""
        status = ReplayStatus(
            is_playing=self.is_playing,
            current_time=self.current_time,
            speed=self.speed,
            total_duration=self.end_time - self.start_time if self.data_list else 0,
            progress=self.current_index / len(self.data_list) if self.data_list else 0,
            symbol=self.symbol,
            period=self.period
        )
        
        message = StatusUpdateMessage(
            data=status.dict(),
            timestamp=int(time.time() * 1000)
        )
        await self._broadcast_message(message.dict())
    
    async def _broadcast_error(self, error_msg: str):
        """广播错误消息"""
        message = ErrorMessage(
            data={"error": "replay_error", "message": error_msg},
            timestamp=int(time.time() * 1000)
        )
        await self._broadcast_message(message.dict())
    
    async def _send_error(self, client_id: int, error_msg: str):
        """发送错误消息给特定客户端"""
        if client_id in self.clients:
            message = ErrorMessage(
                data={"error": "client_error", "message": error_msg},
                timestamp=int(time.time() * 1000)
            )
            try:
                await self.clients[client_id].websocket.send_json(message.dict())
            except Exception as e:
                logger.error(f"发送错误消息失败: {e}")
    
    async def _send_status_update(self, client_id: int):
        """发送状态更新给特定客户端"""
        if client_id in self.clients:
            status = ReplayStatus(
                is_playing=self.is_playing,
                current_time=self.current_time,
                speed=self.speed,
                total_duration=self.end_time - self.start_time if self.data_list else 0,
                progress=self.current_index / len(self.data_list) if self.data_list else 0,
                symbol=self.symbol,
                period=self.period
            )
            
            message = StatusUpdateMessage(
                data=status.dict(),
                timestamp=int(time.time() * 1000)
            )
            
            try:
                await self.clients[client_id].websocket.send_json(message.dict())
            except Exception as e:
                logger.error(f"发送状态更新失败: {e}")
    
    async def _send_heartbeat(self, client_id: int):
        """发送心跳给特定客户端"""
        if client_id in self.clients:
            message = HeartbeatMessage(timestamp=int(time.time() * 1000))
            try:
                await self.clients[client_id].websocket.send_json(message.dict())
            except Exception as e:
                logger.error(f"发送心跳失败: {e}")
    
    async def _broadcast_message(self, message: Dict[str, Any]):
        """广播消息给所有客户端"""
        if not self.clients:
            return
            
        disconnected_clients = []
        
        for client_id, client in self.clients.items():
            try:
                await client.websocket.send_json(message)
            except Exception as e:
                logger.error(f"发送消息给客户端 {client_id} 失败: {e}")
                disconnected_clients.append(client_id)
        
        # 移除断开连接的客户端
        for client_id in disconnected_clients:
            await self.remove_client(client_id)
    
    async def cleanup(self):
        """清理资源"""
        # 停止回放
        self.is_playing = False
        
        # 取消任务
        if self.replay_task and not self.replay_task.done():
            self.replay_task.cancel()
            
        if self.heartbeat_task and not self.heartbeat_task.done():
            self.heartbeat_task.cancel()
        
        # 关闭所有客户端连接
        for client in self.clients.values():
            try:
                await client.websocket.close()
            except Exception:
                pass
        
        self.clients.clear()
        logger.info("回放引擎已清理")
