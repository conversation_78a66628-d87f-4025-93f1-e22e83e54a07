# Feather 文件股票行情回放系统技术设计方案

## 1. 系统概述

本系统旨在实现一个基于本地 Feather 文件的股票行情回放系统，功能类似 TradingView，能够对 tick 级别数据进行实时回放、K 线图展示和交互操作。系统主要包括数据加载、回放引擎、实时图表显示和前端交互控制。

---

## 2. 系统架构图

```
+-----------------------------+
|       Feather 数据文件       |
| (tick级股票数据，时间戳、开盘、最高、最低、收盘、成交量等)|
+-----------------------------+
               |
               v
+-----------------------------+
|  后端数据处理 (FastAPI)      |
| - Feather 文件读取           |
| - 数据预处理/聚合           |
| - WebSocket 数据推送         |
+-----------------------------+
               |
               v
+-----------------------------+
|       前端 (Vue3/React)      |
| - 实时 K 线图 (lightweight-charts) |
| - 时间轴控制 (播放/暂停/快进)  |
| - 图表交互 (缩放/平移/十字线) |
+-----------------------------+
```

---

## 3. 数据流程设计

1. **数据加载**

   * 后端读取本地 Feather 文件
   * 对数据按时间戳排序
   * 可选按时间段或条数分片加载

2. **数据预处理**

   * 将 tick 数据聚合为不同周期 K 线 (1分钟、5分钟、15分钟、1小时、日线)
   * 生成字段：open, high, low, close, volume, timestamp

3. **数据推送**

   * 使用 WebSocket 推送数据到前端
   * 支持回放控制（播放/暂停/倍速）
   * 每条消息格式示例：

```json
{
  "timestamp": 1735779600,
  "open": 2625.098,
  "high": 2625.698,
  "low": 2625.098,
  "close": 2625.298,
  "volume": 12345
}
```

4. **前端渲染**

   * 接收 WebSocket 数据并更新 K 线图
   * 支持实时滚动、缩放、平移和十字光标

---

## 4. API接口设计

### 4.1 HTTP API

| 接口            | 方法  | 描述         | 参数                                            |
| ------------- | --- | ---------- | --------------------------------------------- |
| /load\_data   | GET | 按时间段加载历史数据 | start\_time, end\_time, period (1m, 5m, etc.) |
| /get\_periods | GET | 获取支持的时间周期  | 无                                             |

### 4.2 WebSocket API

| 接口         | 描述       | 数据格式                                                    |
| ---------- | -------- | ------------------------------------------------------- |
| /ws/replay | 实时推送回放数据 | JSON: {timestamp, open, high, low, close, volume}       |
| 控制消息       | 前端发送控制命令 | {action: "play"/"pause"/"seek", value: timestamp/speed} |

---

## 5. 前端组件结构

```
App.vue
 ├─ KLineChart.vue       // 绘制蜡烛图，支持动态更新
 ├─ PlaybackControl.vue  // 播放/暂停/速度/时间轴控制
 ├─ DepthChart.vue       // 可选：盘口深度图
 ├─ VolumeChart.vue      // 可选：成交量柱状图
 └─ store.js             // Pinia管理状态: currentTime, playing, speed
```

### 5.1 组件功能

* **KLineChart.vue**：使用 lightweight-charts 渲染 K 线图，支持 series.update() 增量更新数据
* **PlaybackControl.vue**：播放/暂停按钮，速度选择，时间进度条，可拖动跳转
* **DepthChart.vue**：显示买卖盘深度（可选）
* **VolumeChart.vue**：显示成交量柱状图（可选）

---

## 6. 数据库设计（可选）

如果不使用 Feather 文件进行全量加载，可引入数据库优化历史数据访问：

| 表名    | 字段        | 类型     | 描述    |
| ----- | --------- | ------ | ----- |
| ticks | id        | int    | 主键    |
|       | timestamp | bigint | 秒级时间戳 |
|       | open      | float  | 开盘价   |
|       | high      | float  | 最高价   |
|       | low       | float  | 最低价   |
|       | close     | float  | 收盘价   |
|       | volume    | float  | 成交量   |

可按索引优化按时间查询效率。

---

## 7. 部署方案

1. **后端**

   * 部署 FastAPI + Uvicorn
   * 配置 WebSocket 支持回放
   * 文件存储 Feather 文件在本地或共享网络盘

2. **前端**

   * Vue3 + Vite 打包为静态文件
   * 部署在 Nginx/Apache 上
   * 通过 WebSocket 与后端实时通信

3. **性能优化**

   * Feather 文件分块读取，避免一次性加载大
   * 后端聚合 tick 到不同周期，减轻前端渲染压力
   * 前端增量更新 K 线图而非全量刷新

---

## 8. 总结

本设计方案实现了基于本地 Feather 文件的股票行情回放系统，具备以下特点：

* Tick 数据回放与多周期 K 线展示
* 可实时控制播放速度与跳转时间
* 前端支持缩放、平移、十字光标等交互操作
* 高性能 Feather 文件分块读取与增量渲染

系统可以进一步扩展，增加指标叠加、订单簿显示、策略回测等功能，满足专业股票分析需求。
