 #!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间戳转换工具
将毫秒时间戳转换为可读的日期时间格式
"""

import datetime
from typing import Union


def timestamp_to_datetime(timestamp: Union[int, str], timezone_offset: int = 8) -> str:
    """
    将时间戳转换为可读的日期时间格式
    
    Args:
        timestamp: 时间戳（毫秒或秒）
        timezone_offset: 时区偏移量（小时），默认为8（北京时间）
    
    Returns:
        格式化的日期时间字符串
    """
    # 转换为整数
    if isinstance(timestamp, str):
        timestamp = int(timestamp)
    
    # 判断是毫秒还是秒时间戳
    # 如果时间戳大于10位数，认为是毫秒时间戳
    if timestamp > 9999999999:
        # 毫秒时间戳，除以1000转换为秒
        timestamp_seconds = timestamp / 1000
    else:
        # 秒时间戳
        timestamp_seconds = timestamp
    
    # 转换为datetime对象（UTC时间）
    dt_utc = datetime.datetime.fromtimestamp(timestamp_seconds, tz=datetime.timezone.utc)
    
    # 转换为指定时区时间
    dt_local = dt_utc + datetime.timedelta(hours=timezone_offset)
    
    return dt_local.strftime("%Y-%m-%d %H:%M:%S")


def main():
    """主函数"""
    # 要转换的时间戳
    timestamp = 1740774566
    
    print(f"原始时间戳: {timestamp}")
    print(f"时间戳类型: {'毫秒' if timestamp > 9999999999 else '秒'}")
    
    # 转换为北京时间
    beijing_time = timestamp_to_datetime(timestamp, timezone_offset=8)
    print(f"北京时间 (UTC+8): {beijing_time}")
    
    # 转换为UTC时间
    utc_time = timestamp_to_datetime(timestamp, timezone_offset=0)
    print(f"UTC时间 (UTC+0): {utc_time}")
    
    # 转换为其他时区示例
    tokyo_time = timestamp_to_datetime(timestamp, timezone_offset=9)
    print(f"东京时间 (UTC+9): {tokyo_time}")
    
    new_york_time = timestamp_to_datetime(timestamp, timezone_offset=-5)
    print(f"纽约时间 (UTC-5): {new_york_time}")
    
    # 使用Python标准库的另一种方法验证
    print("\n=== 验证结果 ===")
    dt_verify = datetime.datetime.fromtimestamp(timestamp / 1000)
    print(f"系统默认时区: {dt_verify.strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    main()