import csv
import sqlite3
from pathlib import Path
from typing import Optional

PROJECT_ROOT = Path(__file__).resolve().parents[1]
DB_PATH = PROJECT_ROOT / "data" / "trading_system.db"
CSV_PATH = PROJECT_ROOT / "examples" / "tick_data.csv"
TABLE_NAME = "ticks"

SCHEMA_SQL = f"""
CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
    time INTEGER PRIMARY KEY,
    bid REAL,
    ask REAL,
    vol_bid REAL,
    vol_ask REAL
);
CREATE INDEX IF NOT EXISTS idx_{TABLE_NAME}_time ON {TABLE_NAME}(time);
"""


def parse_time_to_ms(s: str) -> int:
    # 输入示例: "01.08.2025 00:00:00.000 UTC"
    # 格式: DD.MM.YYYY HH:MM:SS.mmm UTC
    from datetime import datetime, timezone

    s = s.replace(" UTC", "")
    dt = datetime.strptime(s, "%d.%m.%Y %H:%M:%S.%f")
    dt = dt.replace(tzinfo=timezone.utc)
    return int(dt.timestamp() * 1000)


def import_csv(csv_path: Path = CSV_PATH, db_path: Path = DB_PATH, truncate: bool = False) -> None:
    db_path.parent.mkdir(parents=True, exist_ok=True)
    conn = sqlite3.connect(str(db_path))
    try:
        conn.executescript(SCHEMA_SQL)
        if truncate:
            conn.execute(f"DELETE FROM {TABLE_NAME}")
            conn.commit()
            conn.execute("VACUUM")

        with csv_path.open("r", newline="", encoding="utf-8") as f:
            reader = csv.DictReader(f)
            to_insert = []
            for row in reader:
                t_ms = parse_time_to_ms(row["UTC"])  # 列名: UTC
                ask = float(row["Ask"]) if row["Ask"] else None
                bid = float(row["Bid"]) if row["Bid"] else None
                vol_ask = float(row.get("Ask Volume") or 0.0)
                vol_bid = float(row.get("Bid Volume") or 0.0)
                to_insert.append((t_ms, bid, ask, vol_bid, vol_ask))

            conn.executemany(
                f"INSERT OR REPLACE INTO {TABLE_NAME} (time, bid, ask, vol_bid, vol_ask) VALUES (?,?,?,?,?)",
                to_insert,
            )
            conn.commit()
        print(f"Imported {len(to_insert)} rows into {db_path}::{TABLE_NAME}")
    finally:
        conn.close()


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Import CSV ticks to SQLite")
    parser.add_argument("--csv", type=str, default=str(CSV_PATH), help="CSV file path")
    parser.add_argument("--db", type=str, default=str(DB_PATH), help="SQLite DB path")
    parser.add_argument("--truncate", action="store_true", help="Truncate table before import")
    args = parser.parse_args()

    import_csv(csv_path=Path(args.csv), db_path=Path(args.db), truncate=args.truncate) 