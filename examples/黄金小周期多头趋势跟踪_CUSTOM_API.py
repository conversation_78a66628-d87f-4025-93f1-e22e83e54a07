import talib as ta

import custom_api.base as base
import custom_api.md as md
from cqfnlib import *
from custom_api import param
from custom_api.context import Context


def init(context):
    """
    init 中初始化一些必要的参数，订阅数据
    初始化方法 - 在回测和实时模拟交易只会在启动的时候触发一次。
    你的算法会使用这个方法来设置你需要的各种初始化配置。
    context 对象将会在你的算法的所有其他的方法之间进行传递以方便你可以章取到。
    """
    strategy_name = "黄金小周期多头趋势跟踪策略"
    version_info = "v1"
    version_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time()))

    qlog.info_f(f"[init]初始化 [{strategy_name}], 版本号: {version_info}, 时间: {version_time}")

    context.param = Param()
    context.bar_last_time = None  # 最新bar数据时间
    context.start_k_num = 50  # 启动最小k线缓存数
    context.k_num_flag = False  # k线缓存数是否满足要求

    # 公共参数 todo
    symbol_info = base.get_contract(context.param.symbol)
    qlog.info_f("[合约信息] symbol_info对象内容: {}", symbol_info)
    context.digits = symbol_info['noDecimal']  # 交易品种价格小数点后的位数
    context.point = eval(f"1e-{context.digits}")  # 有效位数为5, bp=0.00001
    context.slippage_open = 30 * context.point  # 开仓滑点 3个bp 一个bp对应10个Point
    context.slippage_close = 500 * context.point  # 平仓滑点
    context.sl = context.param.sl * context.point  # 止损点数 stop loss
    context.tp = context.param.tp * context.point  # 止盈点数 target profit
    context.sp = context.param.sp * context.point  # 价差点数 spread point
    context.sl_id = {}  # 订单ID与对应的止损价位
    context.tp_id = {}  # 订单ID与对应的止盈价位
    context.openid_closeid = {}  # 待平订单
    context.closeid_openid = {}  # 平仓单与持仓对应
    context.tc = TradeCount(context)  # 平仓单与持仓对应 TODO
    context.openid = ""
    context.closeid = ""

    # 策略参数
    context.start_month = 0
    context.end_month = 11
    context.start_hour = 0
    context.end_hour = 17
    context.ma_short = 30
    context.ma_long = 240
    context.tickCount = 0

    ##########  Tick统计加速器  ##########
    context.tick_counter = 0  # tick运行次数统计
    context.tick_start_time = time.time()  # 开始时间
    context.tick_log_interval = 1000000  # 每10000次tick记录一次日志

    qlog.info_f("[策略参数] MA短期: {}, MA长期: {}, 交易时间: {}-{}月, {}-{}时",
                context.ma_short, context.ma_long, context.start_month, context.end_month,
                context.start_hour, context.end_hour)
    qlog.info_f("[策略参数] 开仓滑点: {}, 平仓滑点: {}, 价差限制: {}",
                context.slippage_open, context.slippage_close, context.sp)


class Param:
    def __init__(self):
        self.symbol = param.get('策略合约')
        self.source = param.get('合约渠道')
        self.bar_frequency = param.get('bar数据频率')
        # self.bar_frequency_day = param.get('bar数据频率day')
        self.lot = param.get('单次下单量') if param.get('单次下单量') is not None else 10000
        self.tp = param.get('止盈点数') if param.get('止盈点数') is not None else 1000
        self.sl = param.get('止损点数') if param.get('止损点数') is not None else 300
        self.sp = param.get("价差点数") if param.get("价差点数") is not None else 300
        # self.protect_flag = True if param.get("推平保护开关") == "开" else False
        # self.move_flag = True if param.get("移动止损开关") == "开" else False


@performance_monitor('onData')
def onData(context, data):
    init_performance_monitor(context)
    # 时间过滤 TODO
    bar = md.query_bars_pro(context.param.symbol, context.param.bar_frequency, context.param.source, count=250,
                            fields=['time', 'close', 'high', 'low', 'open'])

    ############################################ 策略指标计算 ############################################
    # todo 做了修改
    close_arr = np.array([item['close'] for item in bar])
    high_arr = np.array([item['high'] for item in bar])
    low_arr = np.array([item['low'] for item in bar])
    open_arr = np.array([item['open'] for item in bar])
    time_arr = np.array([item['time'] for item in bar])

    # 方向均线
    sma_30 = ta.MA(close_arr, timeperiod=context.ma_short)
    sma_240 = ta.MA(close_arr, timeperiod=context.ma_long)
    macd, macdsignal, macdhist = ta.MACD(close_arr, fastperiod=12, slowperiod=26, signalperiod=9)

    sma_30_now = sma_30[-2]
    
    sma_240_now = sma_240[-2]
    sma_30_prev = sma_30[-3]
    sma_240_prev = sma_240[-3]
    macd_1 = macd[-2]

    dt = datetime.datetime.fromtimestamp(time_arr[-1] / 1000)

    # 每1000次tick输出一次指标信息
    if context.tick_counter % 1000 == 0:
        qlog.info_f("[指标信息] MA30: {:.5f}, MA240: {:.5f}, MACD: {:.5f}, 当前价格: {:.5f}",
                    sma_30_now, sma_240_now, macd_1, close_arr[-1])

    # *************** 多头信号条件 ***************
    # buy开单逻辑：短期均线MA30向上突破长期均线MA240，且MACD指标为正值；
    if context.all_open and context.tc.buy_num < 1 and \
            sma_30_prev < sma_240_prev and sma_30_now > sma_240_now and macd_1 > 0:
        open_price = context.ask + context.slippage_open
        # TODO
        openid = deal.to_order(context.param.symbol, 'B', open_price, context.param.lot, 1,
                               channel_code=context.param.source, pos_type=1)

        context.openid = openid
        qlog.info_f("[多头信号] 触发多头开仓 - MA30: {:.5f}, MA240: {:.5f}, MACD: {:.5f}, 开仓价格: {:.5f}, 订单ID: {}",
                    sma_30_now, sma_240_now, macd_1, open_price, openid)

    # *************** 空头信号条件 ***************
    # sell开单逻辑：短期均线MA30向下突破长期均线MA240，且MACD指标为负值；
    if context.all_open and context.tc.buy_num == 1 and \
            sma_30_prev > sma_240_prev and sma_30_now < sma_240_now and macd_1 < 0:
        open_price = context.bid - context.slippage_open
        # TODO 先撤销，在get_order ，然后在下单。避免超量。
        # 当量超过一定值，会有概率出现 未全额成交，就出发平仓的情况， 因为上面的委托，是 GFD 的模式。
        # 所以他不会被主动撤单。 TODO
        closeid = deal.to_order(context.param.symbol, 'S', open_price, context.param.lot, 2,
                                channel_code=context.param.source,
                                pos_type=1, intention="CLOSE_ORDER", close_order_id=context.openid)
        qlog.info_f("[空头信号] 触发空头平仓 - MA30: {:.5f}, MA240: {:.5f}, MACD: {:.5f}, 平仓价格: {:.5f}, 订单ID: {}",
                    sma_30_now, sma_240_now, macd_1, open_price, closeid)


def onOrder(context):
    # 订单状态：0-初始化,1-运行中,2-订单拒绝,5-订单已超时,6-订单撤销中,7-交易已撤销,8-已结束,9-已提交,99-未明
    # order_status = {0: '初始化', 1: '运行中', 2: '订单拒绝', 5: '订单已超时', 6: '订单撤销中', 7: '交易已撤销', 8: '已结束', 9: '已提交', 99: '未明'}
    #
    # # 获取订单状态描述
    # status_desc = order_status.get(order.get('orderStatus', 99), '未知状态')
    #
    # qlog.info_f("[订单状态] 订单ID: {}, 状态: {} ({}), 方向: {}, 价格: {}, 数量: {}",
    #            order.get('id', 'N/A'),
    #            order.get('orderStatus', 'N/A'),
    #            status_desc,
    #            order.get('side', 'N/A'),
    #            order.get('price', 'N/A'),
    #            order.get('quantity', 'N/A'))

    # 更新交易统计 TODO
    context.tc = TradeCount(context)

    # # 如果订单已完成，记录详细信息
    # if order.get('orderStatus') == '8':  # 已结束
    #     qlog.info_f("[订单完成] 订单ID: {} 已完成, 成交均价: {}, 成交数量: {}",
    #                order.get('id'),
    #                order.get('tradedAvgPrice', 'N/A'),
    #                order.get('tradedQuantity', 'N/A'))


def onTrade(context, trade):
    # 产生成交后触发事件驱动
    qlog.info_f("[成交信息] 成交ID: {}, 订单ID: {}, 方向: {}, 价格: {}, 数量: {}, 成交时间: {}",
                trade.get('id', 'N/A'),
                trade.get('orderId', 'N/A'),
                trade.get('side', 'N/A'),
                trade.get('price', 'N/A'),
                trade.get('quantity', 'N/A'),
                trade.get('tradeTime', 'N/A'))


if __name__ == "__main__":
    # 添加数据库初始化
    from custom_api.database.connection import init_database
    init_database()
    context = Context()
    print(context)
    context.all_open = True
    init(context)
    onData(context, None)
    onOrder(context)
