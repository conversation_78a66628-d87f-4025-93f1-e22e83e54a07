import os
import struct
import pandas as pd
from datetime import datetime, timezone

record_size = 56
file_header_size = 728
file_path = r'D:\code\量化-AICoding\origin_files\XAUUSD60_0.fxt'

# 目标时间戳（UTC）
target_ts = int(datetime(2025, 1, 1, 0, 0, 0, tzinfo=timezone.utc).timestamp())

fmt = 'idddddii'

file_size = os.path.getsize(file_path)
total_records = (file_size - file_header_size) // record_size

with open(file_path, 'rb') as fn:
    left, right = 0, total_records - 1
    found_pos = total_records  # 如果没找到则为空

    # 二分查找第一条 >= target_ts 的记录
    while left <= right:
        mid = (left + right) // 2
        fn.seek(file_header_size + mid * record_size)
        chunk_data = fn.read(record_size)
        ts = struct.unpack(fmt, chunk_data)[0]

        if ts >= target_ts:
            found_pos = mid
            right = mid - 1
        else:
            left = mid + 1

    print(f"找到起始位置: 记录号 {found_pos} (总 {total_records} 条)")

    # 从找到的位置开始顺序读取到末尾
    fn.seek(file_header_size + found_pos * record_size)
    struct_list = []
    while True:
        chunk_data = fn.read(record_size)
        if len(chunk_data) < record_size:
            break
        unpacked_data = struct.unpack(fmt, chunk_data)
        struct_list.append(unpacked_data)

# 转 DataFrame
df = pd.DataFrame(struct_list, columns=[
    "timestamp", "col1", "col2", "col3", "col4", "col5", "int1", "int2"
])

# 转换时间列
df["datetime"] = pd.to_datetime(df["timestamp"], unit='s', utc=True)

# 保存为 feather
output_path = r"/examples/EURUSD_after20250101.feather"
df.to_feather(output_path)

print(f"保存完成: {output_path}，共 {len(df)} 条记录")
