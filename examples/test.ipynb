#%%
import os
import time
import six
from functools import reduce
import struct
import pandas as pd

import pickle
import traceback

file_path = r'D:\code\量化-AICoding\origin_files\XAUUSD60_0.fxt'
record_size = 56  # 每条记录字节数
file_header_size = 728  # 文件头大小
percent = 0.00001  # 读取最后 10% 数据
#%%
file_size = os.path.getsize(file_path)

# 数据区大小
data_size = file_size - file_header_size

# 计算从哪里开始读
start_pos = file_header_size + int(data_size * (1 - percent))

# 向前对齐到记录边界
offset_in_record = (start_pos - file_header_size) % record_size
start_pos -= offset_in_record

struct_list = []
fmt = 'idddddii'

with open(file_path, 'rb') as fn:
    fn.seek(start_pos, 0)  # 从对齐后的位置开始读
    while True:
        chunk_data = fn.read(record_size)
        if len(chunk_data) < record_size:  # 不足一条记录就跳过
            break
        unpacked_data = struct.unpack(fmt, chunk_data)
        struct_list.append(unpacked_data)

test_df = pd.DataFrame(struct_list)
print(test_df)
#%%
import pandas as pd

# feather 文件路径
file_path = r"D:\code\量化-AICoding\examples\EURUSD_after20250101.feather"

# 读取 feather 文件
df = pd.read_feather(file_path)

#%%
print(df.columns)  # 查看实际列名
print(df.shape)    # 查看行数和列数
print(df.head())  
#%%

df.columns = ['CurrentBarTime', 'Open', 'Low', 'High', 'Close', 'Volume', 'IncomingTickTime', 'Flag','datetime']
df['CurrentBarTime'] = df['CurrentBarTime'].apply(lambda x: time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(x)))
df['IncomingTickTime'] = df['IncomingTickTime'].apply(lambda x: time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(x)))
    

print(df.head())       # 查看前几行
print(df.tail())       # 查看前几行

print(df.columns)      # 查看列名
print(len(df))         # 查看行数
